# Project Leaf Backend

<!-- TABLE OF CONTENTS -->
<details open="open">
	<summary><h2 style="display: inline-block">Table of Contents</h2></summary>
  <ol>
    <li>
      <a href="#getting-started">Getting Started</a>
      <ul>
        <li><a href="#quick-setup">Quick setup</a></li>
        <li><a href="#prerequisites">Prerequisites</a></li>
        <li><a href="#development-setup">Development Setup</a></li>
        <li><a href="#environment-variables">Environment variables</a></li>
        <li><a href="#secrets">Secrets</a></li>
        <li>
          <a href="#docker-database-setup-optional"
            >Docker database setup (optional)</a
          >
        </li>
        <li>
          <a href="#testing-google-cloud-services-in-development"
            >Testing Google Cloud services in development</a
          >
        </li>
        <li><a href="#set-up-cloud-storage">Set up Cloud Storage</a></li>
        <li><a href="#starting-phoenix-server">Starting Phoenix server</a></li>
      </ul>
    </li>
    <li>
      <a href="#defining-data-clean-up-workers">Defining Data Clean-Up Workers</a>
    </li>
    <li>
      <a href="#decrypting-csv-files-from-link-registry"
        >Decrypting CSV files from Link Registry</a
      >
    </li>
    <li>
      <a href="#rotating-link-registry-ssh-and-encryption-keys">
        Rotating Link Registry SSH and encryption keys
      </a>
    </li>
    <li><a href="#common-tasks">Common Tasks</a></li>
    <ul>
      <li><a href="#reset-elixirls">Reset ElixirLS</a></li>
      <li><a href="#running-nginx-locally">Running NGINX Locally</a></li>
      <li><a href="#fetch-hex-dependencies">Fetch Hex Dependencies</a></li>
      <li><a href="#testing">Testing</a></li>
      <li><a href="#code-checking">Code Checking</a></li>
      <li><a href="#generating-schema">Generating Schema</a></li>
      <li><a href="#generating-gettext">Generating gettext</a></li>
      <li><a href="#updating-dispatch-routes">Updating dispatch routes</a></li>
      <li>
        <a href="#running-iex-on-a-live-deployment"
          >Running iex on a live deployment</a
        >
      </li>
      <li><a href="#creating-sftp-user">Creating SFTP user</a></li>
    </ul>
    <li><a href="#registry-integration">Registry Integration</a></li>
    <li><a href="#writing-tests">Writing Tests</a></li>
    <li><a href="#learn-more">Learn more</a></li>
  </ol>
</details>

<!-- GETTING STARTED -->

## Getting Started

### Quick setup

To help you speed up getting started, you can run a script that installs tools (`elixir`, `erlang`, `nodejs`, `postgresql`, `imagemagick`, `asdf` and `direnv`) that we use.

Make sure you are in the root directory of the `leaf_be` project.

Run the following script:

```sh
# give permissions to run the script
chmod +x ./scripts/dev_onboarding.sh

# run the script
./scripts/dev_onboarding.sh
```

After the above has run, you can continue from the `elixir --version` version check in the next section (Prerequisites).

### Prerequisites

If you successfully ran the `dev_onboarding` script above you can skip this section.

- [Elixir](#elixir-installation)
- [Homebrew](https://brew.sh/)
- Postgres - either via [brew](https://wiki.postgresql.org/wiki/Homebrew) or [via docker](#docker-postgres-setup)
- [Postico](https://www.macupdate.com/app/mac/53411/postico)
- Imagemagick: `brew install imagemagick`

 -- wkhtmltopdf 
 https://wkhtmltopdf.org/downloads.html download and install from here

* Elixir + Node installation

  We recommend using [asdf](https://asdf-vm.com/#/core-manage-asdf) to install Elixir as it will also take care of your Node tooling (see below for full setup):

  ```sh
  # Install with brew
  brew install asdf

  # Add `asdf` to your zsh profile:
  echo -e "\n. $(brew --prefix asdf)/asdf.sh" >> ~/.zshrc

  On new macOS, you might encounter BUS errors when trying to install erlang. The configuration below can prevent that.
  - https://elixirforum.com/t/bus-error-after-upgrading-to-sonoma-beta/56354
  - https://github.com/asdf-vm/asdf-erlang/issues/275
  
  ```sh
  KERL_CONFIGURE_OPTIONS="--disable-jit"
  ```

  # Add asdf plugins for Elixir, Erlang, and Node
  asdf plugin add elixir
  asdf plugin add erlang
  asdf plugin add nodejs

  # Install project tooling
  asdf install

  # Check correct versions are installed (refer to .tool-versions):
  elixir --version
  node -v

  # Check yarn is available:
  yarn -v

  # If not:
  corepack enable
  corepack prepare yarn@1.22.19 --activate

  # If you couldn't even find corepack, do the following and then repeat the above:
  npm i -g corepack

  # If after all of the above yarn says 'command not found', try:
  asdf reshim nodejs
  ```

## Development setup

### Hosts file

We use localhost.com as our domain for local development. This helps us replicate production URLs like `cba.localhost.com`, where we match the subdomain to a specific customer. To set this up, you need to add the following to your hosts file:

```bash
sudo vim /etc/hosts
```

Add these lines into your hosts file:

```bash
127.0.0.1 localhost.com
127.0.0.1 cba.localhost.com # this can be replaced with any company you are testing
```

### Environment variables

You can source `.env` before each time your run the server, but [direnv](https://direnv.net/) will do it for you when you `cd` into the project:

#### Install direnv

If you successfully ran the `dev_onboarding` script above you can skip installation.

```sh
brew install direnv
```

- Usually `direnv` looks for an `.envrc` file rather than a `.env`. If you want to use bash commands in the file, go with `.envrc`. That said, `direnv` can be configured to detect both with the following config:

```toml
# ~/.config/direnv/direnv.toml

[global]
load_dotenv = true
```

#### Set up your environment variables

Now ensure you have a `.envrc` / `.env` with the right environment variables. Copy our example file:

```sh
cp .env.example .envrc

# OR

cp .env.example .env
```

Most of the defaults should work out of the box, however you will need to change the env vars under the "Google" section and "File uploads" section.

See <a href="#testing-google-cloud-services-in-development">Testing Google Cloud services in development</a> and <a href="#set-up-cloud-storage">Set up Cloud Storage</a> below.

### Secrets

Ask your manager to set you up on 1password. You will need to get the following secrets:

- To read or edit, create a `master.key` file using the value stored in 1password `leaf_be secrets encryption key`

  Preferred method: Using 1password cli

  - Get the 1password cli and login
    - Command line download: https://app-updates.agilebits.com/product_history/CLI
    - Sign in: https://support.1password.com/command-line
  - Run the command

  ```sh
  echo `op item get lj3iyjseuvevdadznqrmkpnmha --reveal --fields password` > apps/gaia/priv/secrets/master.key
  ```

  If the item can't be found you will need to ask your manager to give you appropriate permissions.

  Alternative method: You can also just go to 1password and get it!

  To view or edit secrets (with vscode), run:

  ```sh
  (cd apps/gaia && EDITOR='code --wait' mix EncryptedSecrets.Edit)
  ```

  CLI download: https://app-updates.agilebits.com/product_history/CLI.
  Instructions: https://support.1password.com/command-line/.
  Secrets in the `secrets/yml.enc` file use https://github.com/kieraneglin/encrypted_secrets_ex.

- Note : After you add a section of configuration, please add in application.ex

### Testing Google Cloud services in development

First, authenticate via the GCloud CLI ([install it here](https://cloud.google.com/sdk/docs/install), if you haven't already):

```sh
gcloud auth login
```

Follow the prompts to login with your InvestorHub Google account. Then, set the project ID:

```sh
gcloud config set project leaf-343323
```

You'll also need to get the GOOGLE_APPLICATION_CREDENTIALS `leaf-credentials.json` file for the leaf service account from a friendly dev (or 1Password under 'leaf-credentials.json') and, depending on your GCloud install, put it in something like one of:

- `$HOME/.gcloud/leaf-343323/credentials.json`
- `/Users/<USER>/.config/gcloud/fresh-equities/leaf-credentials.json`

Just ensure you set the GOOGLE_APPLICATION_CREDENTIALS env var to the correct file path.

### Set up Cloud Storage

We are now using Cloud Storage for development environment. Each dev will have a unique bucket `leaf-dev-wk` where `wk` is your initials. To make this happen, you need to have `GOOGLE_STORAGE_BUCKET` in your local environment variable, and comment out `config :goth, disabled: true` in `config/runtime.exs`. You also need to create a bucket in [here](https://console.cloud.google.com/storage/browser?project=leaf-343323). Make sure to select `us-west1` as a single region.

![Screen Shot 2022-04-07 at 10 14 54 am](https://user-images.githubusercontent.com/********/*********-d779e38f-68ac-4800-9ced-3273dfa420ba.png)

And, select fine-grained access control.

![Screen Shot 2022-04-07 at 10 15 10 am](https://user-images.githubusercontent.com/********/162095470-aa65c491-77c8-4ec4-81e1-ad0f7a184800.png)

### Enabling Uploads to your Local Cloud Storage Bucket

Update the `cors` configuration of your bucket is as follows:

```sh
gcloud storage buckets update gs://BUCKET_NAME --format="default(cors)"
```

Check that the `cors` configuration is set correctly:

```sh
gcloud storage buckets describe gs://BUCKET_NAME --format="default(cors_config)"
```

You should get this:

```sh
cors:
- maxAgeSeconds: 3600
  method:
  - GET
  - HEAD
  - OPTIONS
  - PUT
  origin:
  - http://localhost.com:3001
  responseHeader:
  - '*'
```

If not, create a new JSON file with your `cors` configuration:

```json
[
  {
    "origin": ["http://localhost.com:3001"],
    "method": ["GET", "HEAD", "OPTIONS", "PUT"],
    "responseHeader": "*",
    "maxAgeSeconds": 3600
  }
]
```

Then update your bucket's `cors` configuration using the newly created file:

```sh
gcloud storage buckets update gs://BUCKET_NAME --cors-file=CORS_CONFIG_FILE
```

You can read more about `cors` configuration with GCP here: [https://cloud.google.com/storage/docs/cors-configurations](https://cloud.google.com/storage/docs/cors-configurations)

### Setting up your local database and starting the Phoenix server

Pre-requisties:

We Need to use psql v17 in order to use pgvector (at least when installing via brew)

```sh
brew install postgresql@17
brew install pgvector
brew services start postgresql@17
```

1. Create a new db called `leaf_dev` by running `createdb leaf_dev`, or create it with Postico
2. Create a new role for the default database:

```sh
psql
```

Then in the psql shell:

```sql
CREATE ROLE posgres SUPERUSER LOGIN PASSWORD 'postgres';
```

3. Integrate Phoenix LiveView with Javascript, run: `cd apps/hades/assets` -> `yarn`
4. Return to the project root, then run `mix setup`.

5. If you didn't <a href="#environment-variables">setup direnv</a>, source `.env` or `.app.env` before running the server. Otherwise, skip this step.

6. Make sure all secrets and environment variables have been set, then start Phoenix endpoint:

- Standard: `mix phx.server`
- Interactive: `iex -S mix phx.server`

Now you can visit from your browser:

- [`localhost.com:4001`](http://localhost.com:4001) for athena (company dashboard)
- [`localhost.com:4002`](http://localhost.com:4002) for hades (super admin dashboard)
- [`localhost.com:4003`](http://localhost.com:4003) for hermes (company investor centres)
- [`localhost.com:4005`](http://localhost.com:4005) for transactional email template previews
- [`localhost.com:4006`](http://localhost.com:4006) for public API to be used by our clients
- [`localhost.com:36245`](http://localhost.com:36245) for sent transactional email viewer (Swoosh)

**To start your Phoenix server inside a docker container:**

- Install [Docker](https://docs.docker.com)
- Install the [Remote Development extension pack](https://aka.ms/vscode-remote/download/extension) for Visual Studio Code
- Open the project folder in the container

### Docker Database Setup (optional)

- Install docker desktop: https://www.docker.com/products/docker-desktop

- Start a postgres database:

```sh
docker run --rm --name postgres -e POSTGRES_PASSWORD=postgres -d -p 5432:5432 -v $HOME/docker/volumes/postgres:/var/lib/postgresql/data postgres
```

- **OR** use the `docker-compose.yml` config to run all Leaf + InvestorHub databases for dev and testing:

```sh
docker compose up
```

Ensure you run one of the above before running the server or test suite.

- If you **aren't** using `direnv`, ensure you have `.app.env` or rename `.env` to `.app.env` under `apps/gaia/`.

- If on development, change DB_HOST to "localhost.com" (refer to `.env.example` for suggested environment variables)

### Seeding dev db

```sh
mix run apps/gaia/priv/repo/seeds/dev.exs
```

This will create:

- a company profile with the ticker CBA (make sure your hosts file has the line `127.0.0.1 cba.localhost.com`)
- some users for the company

What it won't create:

- an InvestorHub admin user

To create an InvestorHub admin user, run the server and navigate to `localhost.com:4002`. Login via Google with your investorhub.com email.
Now in the database, there should be a new row in the `admins_users` table. Change the role to be `admin`.

## Defining Data Clean-Up Workers

We use Playwright on the front-end to run E2E Browser tests on our most important features/workflows. These tests put a lot of
junk in our database though, so when in dev or staging we expose a `/workers/enqueue` route to trigger Oban clean-up workers in the Athena + Hermes APIs.

These routes accept a POST request containing a body like so:

```js
{
  worker: "CleanTestInvestorUsers";
}
```

The convention is for `worker` to correspond to the aliased module name of an Oban worker we define to clean up test data for
a particular context. In the above example, `"CleanTestInvestorUsers"` refers to the `Gaia.Workers.Playwright.CleanTestInvestorUsers`.
Any clean-up workers you define should follow the `Gaia.Workers.Playwright.CleanTestContext` convention, and you should add a
corresponding `case` clause to either the Athena or Hermes `EnqueueWorkerController` to facilitate it being enqueued by the front-end.

### How do I know what's test data and what's not?

Test users, whether they be companies or investors, should be created with [Mailosaur](https://mailosaur.com/app) email addresses.
Doing this means we can start most any clean-up by querying for users with a Mailosaur email address, deleting any data related to those
users, and ultimately deleting the users themselves.

## Decrypting CSV files from Link Registry

Occasionally you may find that our Link Registry import task is misbehaving, and on such an occasion you will likely want to download the problem file to inspect it and see what's going on. This is non-trivial, because they're [GPG](https://en.wikipedia.org/wiki/GNU_Privacy_Guard) ([a.k.a PGP](https://en.wikipedia.org/wiki/Pretty_Good_Privacy)) [encrypted](https://en.wikipedia.org/wiki/GNU_Privacy_Guard).

### 1. Install and setup GnuPG

First you'll need the `gpg` command line tool:

```sh
brew install gnupg
```

Once that's finished, you'll need to import the keys Link used to encrypt the file. These are in 1Password in the 'Leaf' folder. Prepare the keys for import:

- Copy the entire contents of the "Link PGP Encrypted Private Key" secure note on 1Password and paste it in a new text file `~/link_gpg_keys/link_private_key.asc`
- Copy the entire contents of the "Link PGP Public Key" secure note on 1Password and paste it in a new text file `~/link_gpg_keys/link_public_key.asc`

Now import the keys into GnuPG:

```sh
gpg --import ~/link_gpg_keys/link_private_key.asc
```

You'll be prompted to enter a password, this is in 1Password under "Link PGP Key Passphrase".

Then the public key:

```sh
gpg --import ~/link_gpg_keys/link_public_key.asc
```

### 2. Download the encrypted CSV you want

When we download files from Link's Managed File Transfer server, they are immediately deleted. For this reason we back up the encrypted files in Google Object Storage. [The Link folder is here](https://console.cloud.google.com/storage/browser/leaf-prod/uploads/registers/link), and you'll find the CSV files sorted in folders by the company ticker e.g `ivx/IVX_Trans_to_01_05_2023.csv.pgp`.

### 3. Decrypt the CSV file

Finally, with `gpg` initialised with the correct keys and the file you're after on your machine, you can decrypt it:

```sh
gpg --decrypt ~/path/to/file.csv.pgp > ~/path/to/new_decrypted.csv
```

You'll again be prompted for the "Link PGP Key Passphrase" password. All done!

**NOTE: This PGP Key expires February 26th, 2025.**

## Rotating Link Registry SSH and encryption keys

To generate keys for to encrypt file (we called it PGP in our system) to Link

```
gpg --full-generate-key

# Choose RSA and RSA
# Type 4096 as keysize
# Type 1y for the expiry
# Put your name
# Put your email address
# Put a passphrase and keep this in 1 password under `Link Registry AU` vault
# Type O for Okay to finish and generating the keys

gpg --export --armor "<EMAIL>" > pgp_public_key.asc
gpg --export-secret-keys --armor "<EMAIL>" > pgp_private_key.asc

# Test that the keys work
gpg --encrypt --recipient "<EMAIL>" file.txt
gpg --decrypt file.txt.gpg > file.txt

# Share the file `pgp_public_key.asc` with Link
```

To generate keys to SSH into Link server

```
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Enter /Users/<USER>/.ssh/id_rsa as the file name
# Enter a passphrase to keep it secure
# Share the file `id_rsa.pub` with Link

# Convert the private key from `OpenSSH format` to `RSA format` and rename the file to `.pem` for testing locally
# The converted version is the one we are putting on GCP Secret Manager
ssh-keygen -p -m PEM -f /Users/<USER>/.ssh/id_rsa
```

Once you have shared the keys with Link and decide on a date to change the keys, don't forget to update the following secrets on `Google Secret Manager`:
- link-pgp-private-key
- link-pgp-private-key-passphrase
- link-pgp-public-key
- link-ssh-private-key
- link-ssh-private-key-passphrase
- link-ssh-public-key

As per 12 February 2025, the person to contact on Link side is [David Timms](mailto:<EMAIL>).

## Common Tasks

### Reset ElixirLS

1. Quit VS Code
2. From the project root: `rm -rf .elixir-ls`
3. Open VS Code

### Running NGINX locally

Download NGINX

```sh
brew install NGINX
```

Run NGINX with our custom config in the root directory of the project

```sh
nginx -c $(pwd)/.cloudbuild/nginx.conf
```

To generate a local environment friendly version of your nginx.conf, run

```sh
sed 's/fresh\\\.xyz/(.*?)/g' .cloudbuild/nginx.conf > .cloudbuild/dev.nginx.conf
```

To run the local environment version

```sh
nginx -c $(pwd)/.cloudbuild/dev.nginx.conf
```

To test the proxying to the applications make sure that they are running locally as well with `mix phx.server`

You can test the routes by visiting <app_name>.localhost.com:8080 e.g hermes.localhost.com:8080

To stop the NGINX process run

```sh
nginx -s quit
```

For more information see the official NGINX website [here](http://nginx.org/)

### Fetch Hex dependencies

```sh
mix deps.get
```

### Testing

```sh
# On first run, you may need to create the test database
createdb fresh_test

# Run the tests
mix test
```

### Code checking

To run all the checks in parallel (compiler, unused_deps, formatter, ex_unit, credo, dialyzer, npm_test, sobelow):

```sh
mix check
```

To fix errors automatically

```sh
mix check --fix
```

### Generating schema

For Athena

```sh
mix absinthe.schema.sdl --schema AthenaWeb.Schema graphql/athena.schema.graphql
```

For Hermes

```sh
mix absinthe.schema.sdl --schema HermesWeb.Schema graphql/hermes.schema.graphql
```

### Generating gettext

```sh
mix gettext.extract --merge --local-en

```

### Updating dispatch routes

```sh
gcloud app deploy .cloudbuild/dispatch.yaml
```

### Running iex on a live deployment

- Go to https://console.cloud.google.com/appengine/instances
- Find the instance, near the bottom, click the down arrow and then click view gcloud command
- You should get something like this

```sh
gcloud app instances ssh "aef-default-20220406t035029-n6wm" --service "default" --version "20220406t035029" --project "leaf-343323"
```

- Use this to SSH into the VM through your CLI
- To start iex on the running application

```sh
sudo su
docker exec -it gaeapp /bin/sh
cd /app/bin
./leaf_be remote
```

- This allows you to run an iex shell on the live deployment. You can call module functions etc as you would in a local iex shell

### Creating SFTP User

When you need to create an SFTP user for registrar to upload client's registries data, run:

```sh
gcloud compute ssh uk-sftp --zone=europe-west2-a --command="bash -s acme 'MyS3cureP@ss!'" < ./scripts/create_sftp_user.sh
```

For more information, please refer to the implementation at `create_sftp_user.sh`

Once created, please share it via a secure platform such as 1password

## Registry Integration

As per March 2024, we only support registry integration for ASX companies who are with `Automic`, `Boardroom`, `Link` and `Xcend`. We also have only been considering [Fully Paid Ordinary Shares](https://www.investopedia.com/terms/f/fullypaidshares.asp) and not [Escrowed Shares](https://www.investopedia.com/terms/e/escrowedshares.asp).

In most cases we can access two reports from the registry:

- Register report / listing report: who and how many shares they hold by the end of the day
- Transaction report / movement report: who moved (buy/sell/transfer) shares between the date range

In our database, we represent this information into 3 tables:

- `registers_shareholdings` - contains the account metadata (i.e. name, email, address, etc.)
- `registers_share_movements` - equivalent to transaction report
- `registers_daily_holdings` - equivalent to register report

Generic registry import workflow (each registries will have slightly different behaviour):

1. The cron `Gaia.Workers.ImportRegister` will trigger and import the data for each companies everyday
2. First step is to get the latest **register report** and update the data on **registers_shareholdings** table
3. Then, get the **transaction report** from last imported date until latest available date and insert them into **registers_share_movements** table
4. Lastly, we need to populate the **registry_daily_holdings** table using the data from **registers_share_movements** table
5. Side note - during the import process, there are other workflows that might occur such as calculating counter caches to improve query perfomance and also sending **new shareholder welcome email**. Detailed information can be seen on the code

There are two entry points that will trigger registry import. You can start from them to understand more about the registry import workflow:

1. The cron `Gaia.Workers.ImportRegister` that runs everyday
2. On Hades, there is a button `HadesWeb.LiveComponents.RegistryData.ConnectButton` for CSM to click connect to registry which will trigger the import for the first time

## Writing Tests

### Questions to help decide if we need to write tests

- Will the clients churn if the feature is not working as intended? i.e. Clients is likely to churn if email is sent to unintended recipients.
- Will it damages our reputation if the feature is not working as intended? i.e. Data breach is likely to kill public trusts in our company.
- Will it damages our clients reputation if the feature is not working as intended? i.e. Unable to complete basic actions such as login, view announcements, comments and likes are likely to damage the reputation of our clients.

### Some features that we have identified with greatest risks

- Sending sensitive emails to the wrong recipients
- Hermes website performing badly could reflect poorly on our client share price

### Sample

```
defmodule Gaia.ContactsTest do
  # Gaia.DataCase also defines `use Gaia.Factory` inside it
  # It gives us access to functions under Gaia.Factory
  use Gaia.DataCase

  describe "contacts_query/2" do
    # This setup will be run on each `test` block within this `describe` block
    setup do
      {:ok, company_profile: company_profile()}
    end

    test "Create 3 contacts and retrieve all of them successfully", %{company_profile: company_profile} do
      contact1 = contact(company_profile)
      contact2 = contact(company_profile)
      contact3 = contact(company_profile)

      results =
        company_profile.id
        |> Contacts.contacts_query()
        |> Repo.all()

      assert length(results) == 3
      assert in_list(results, contact1)
      assert in_list(results, contact2)
      assert in_list(results, contact3)
    end

    test "Create a contact with custom attributes", %{company_profile: company_profile} do
      email_input = "<EMAIL>"
      first_name_input = "Liberty"
      last_name_input = "Cha Cha"

      contact = contact(company_profile, %{email: email_input, first_name: first_name_input, last_name: last_name_input})

      assert contact.email == email_input
      assert contact.first_name == first_name_input
      assert contact.last_name == last_name_input
    end
  end
end
```

### References

- Sample GraphQL test: `AthenaWeb.Workflows.SearchContactIndividualFilterTest`
- Sample Unit test: `Gaia.ContactsTest`
- Test data generator code: `Gaia.Factory`

## Learn more

- Official website: http://www.phoenixframework.org/
- Guides: http://phoenixframework.org/docs/overview
- Docs: https://hexdocs.pm/phoenix
- Mailing list: http://groups.google.com/group/phoenix-talk
- Source: https://github.com/phoenixframework/phoenix
- Ready to run in production? Please [check our deployment guides](http://www.phoenixframework.org/docs/deployment).
