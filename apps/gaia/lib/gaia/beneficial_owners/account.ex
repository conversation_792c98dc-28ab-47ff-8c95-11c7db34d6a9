defmodule Gaia.BeneficialOwners.Account do
  @moduledoc false

  use Ecto.Schema

  import Ecto.Changeset

  @required [
    :company_profile_id,
    :account_name,
    :contact_id
  ]

  @optional [
    :address_line_one,
    :address_line_two,
    :address_city,
    :address_state,
    :address_postcode,
    :address_country,
    :depot
  ]

  schema "beneficial_owner_accounts" do
    belongs_to :company_profile, Gaia.Companies.Profile
    belongs_to :contact, Gaia.Contacts.Contact

    has_many :beneficial_owner_holdings, Gaia.BeneficialOwners.Holding, foreign_key: :beneficial_owner_account_id

    field :account_name, :string
    field :address_line_one, :string
    field :address_line_two, :string
    field :address_city, :string
    field :address_state, :string
    field :address_postcode, :string
    field :address_country, :string
    field :depot, :string

    timestamps()
  end

  def changeset(attrs), do: changeset(%__MODULE__{}, attrs)

  def changeset(%__MODULE__{} = account, attrs) do
    account
    |> cast(attrs, @required ++ @optional)
    |> validate_required(@required)
  end
end
