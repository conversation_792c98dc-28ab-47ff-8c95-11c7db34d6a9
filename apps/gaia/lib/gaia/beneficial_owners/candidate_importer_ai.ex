defmodule Gaia.BeneficialOwners.CandidateImporterAi do
  @moduledoc """
  Import candidates from a PDF or CSV file using AI
  """
  alias Gaia.BeneficialOwners

  require Logger

  @callback ai_parsing(content :: term(), type :: atom(), custom_prompt :: String.t(), is_uk :: boolean()) ::
              {:ok, map()} | {:error, String.t()}

  @ai_chunk_size 30
  @import_chunk_size 50
  @pdf_chunk_size 5

  defp ai_parsing_impl, do: Application.get_env(:gaia, CandidateImporterAi)[:ai_parsing]

  def extract_and_import_past_report(content, report_id, past_report_date, custom_prompt, is_uk, file_type) do
    notify_pub_sub(report_id, "Reading file...")

    with {:ok, chunked_content} <- chunk_file_content(content, file_type),
         {:ok, report} <- {:ok, BeneficialOwners.get_report_by_id!(report_id)},
         {:ok, _} <-
           BeneficialOwners.update_report(report, %{
             report_date: past_report_date,
             is_past: true
           }),
         {:ok, candidates} <-
           process_chunks_and_import_past_report(
             chunked_content,
             report_id,
             custom_prompt,
             is_uk
           ) do
      {:ok, candidates}
    else
      {:error, error} ->
        {:error, "Error extracting or importing candidates: #{inspect(error)}"}
    end
  end

  def extract_and_import_candidates(content, report_id, parent_id, custom_prompt, is_uk, file_type) do
    notify_pub_sub(report_id, "Reading file...")

    with {:ok, chunked_content} <- chunk_file_content(content, file_type),
         {:ok, candidates} <-
           process_chunks_and_import(chunked_content, file_type, report_id, parent_id, custom_prompt, is_uk) do
      {:ok, candidates}
    else
      {:error, error} ->
        {:error, "Error extracting or importing candidates: #{inspect(error)}"}
    end
  end

  def chunk_file_content({first_page_content, chunked_content}, :pdf) do
    case ai_parsing_headers(first_page_content, :get_headers_pdf, "") do
      {:ok, headers} ->
        chunks_with_headers =
          Enum.map(chunked_content, fn chunk -> {Enum.join(headers["headers"], ","), chunk} end)

        {:ok, chunks_with_headers}

      {:error, error} ->
        {:error, "Error extracting headers: #{inspect(error)}"}
    end
  end

  def chunk_file_content(content, :csv) do
    lines = String.split(String.replace(content, "\r", "\n"), "\n", trim: true)

    case ai_parsing_headers(lines |> Enum.take(50) |> Enum.join("\n"), :get_headers, "") do
      {:ok, headers} ->
        chunks = Enum.chunk_every(lines, @ai_chunk_size)

        chunked_contents =
          chunks
          |> Enum.with_index()
          |> Enum.map(fn {chunk_lines, index} ->
            if index == 0 do
              Enum.join(chunk_lines, "\n")
            else
              Enum.join(headers["headers"], ",") <> "\n" <> Enum.join(chunk_lines, "\n")
            end
          end)

        {:ok, chunked_contents}

      {:error, error} ->
        {:error, "Error extracting headers: #{inspect(error)}"}
    end
  end

  def chunk_file_content(content, file_type) when file_type in [:xlsx, :xls] do
    {:ok, workbook} = XlsxReader.open(content, source: :binary)

    {:ok, rows} =
      workbook
      |> XlsxReader.sheet_names()
      |> List.first()
      |> then(&XlsxReader.sheet(workbook, &1))

    case ai_parsing_headers(
           rows |> Enum.take(50) |> Enum.map_join("\n", fn row -> Enum.map_join(row, ",", &format_cell/1) end),
           :get_headers,
           ""
         ) do
      {:ok, headers} ->
        headers_xlsx = Enum.map_join(headers["headers"], ",", &format_cell/1)

        chunks =
          rows
          |> Enum.chunk_every(@ai_chunk_size)
          |> Enum.filter(fn chunk ->
            Enum.any?(chunk, fn row -> row != [] end)
          end)

        chunked_contents =
          chunks
          |> Enum.with_index()
          |> Enum.map(fn {chunk_rows, index} ->
            rows_xlsx =
              Enum.map_join(chunk_rows, "\n", fn row ->
                Enum.map_join(row, ",", &format_cell/1)
              end)

            if index == 0 do
              rows_xlsx
            else
              headers_xlsx <> "\n" <> rows_xlsx
            end
          end)

        {:ok, chunked_contents}

      {:error, error} ->
        {:error, "Error extracting headers: #{inspect(error)}"}
    end
  end

  defp process_chunks_and_import_past_report(chunks, report_id, custom_prompt, is_uk) do
    notify_pub_sub(report_id, "Sending to AI...")
    notify_pub_sub(report_id, {:total_chunks, length(chunks)})

    processed_results =
      chunks
      |> Task.async_stream(
        fn chunk -> extract_shareholders_from_past_report(chunk, report_id, custom_prompt, is_uk) end,
        max_concurrency: 4,
        timeout: 90_000
      )
      |> Stream.flat_map(fn
        {:ok, shareholders} -> shareholders
        _ -> []
      end)
      |> Enum.to_list()

    notify_pub_sub(report_id, "Importing candidates from AI...")

    merged_registered_holders =
      processed_results
      |> Enum.reduce(%{}, fn registered_holder, acc ->
        key =
          "#{registered_holder["account_name"]}#{registered_holder["address_line_one"]}#{registered_holder["address_postcode"]}"

        Map.update(acc, key, registered_holder, &merge_registered_holders(&1, registered_holder))
      end)
      |> Map.values()

    merged_registered_holders
    |> Task.async_stream(
      fn registered_holder ->
        import_past_report_candidate(registered_holder, report_id)
      end,
      max_concurrency: 4,
      timeout: 90_000
    )
    |> Enum.reduce(%{total_imported: 0, errors: []}, &accumulate_results/2)
    |> case do
      %{total_imported: count, errors: []} when count > 0 ->
        {:ok, %{candidates_count: count, has_errors: false}}

      %{total_imported: count, errors: _errors} when count > 0 ->
        # Partial success
        {:ok, %{candidates_count: count, has_errors: true}}

      %{errors: errors} when errors != [] ->
        {:error, "Failed to process chunks: #{inspect(errors)}"}

      _ ->
        {:error, "No candidates were imported"}
    end
  end

  defp process_chunks_and_import(chunks, file_type, report_id, parent_id, custom_prompt, is_uk) do
    notify_pub_sub(report_id, "Sending to AI...")
    notify_pub_sub(report_id, {:total_chunks, length(chunks)})

    processed_results =
      chunks
      |> Task.async_stream(
        fn chunk -> extract_shareholders(chunk, file_type, report_id, custom_prompt, is_uk) end,
        max_concurrency: 4,
        timeout: 90_000
      )
      |> Stream.flat_map(fn
        {:ok, shareholders} -> shareholders
        _ -> []
      end)

    notify_pub_sub(report_id, "Processing results from AI...")

    merged_shareholders =
      processed_results
      |> Enum.reduce(%{}, fn candidate, acc ->
        key = "#{candidate["account_name"]}#{candidate["address_line_one"]}#{candidate["address_postcode"]}"
        Map.update(acc, key, candidate, &merge_candidates(&1, candidate))
      end)
      |> Map.values()

    notify_pub_sub(report_id, "Importing candidates...")

    merged_shareholders
    |> Enum.chunk_every(@import_chunk_size)
    |> Task.async_stream(
      &import_candidate_chunk(&1, report_id, parent_id),
      max_concurrency: 4,
      timeout: 90_000
    )
    |> Enum.reduce(%{total_imported: 0, errors: []}, &accumulate_results/2)
    |> case do
      %{total_imported: count, errors: []} when count > 0 ->
        {:ok, %{candidates_count: count, has_errors: false}}

      %{total_imported: count, errors: _errors} when count > 0 ->
        # Partial success
        {:ok, %{candidates_count: count, has_errors: true}}

      %{errors: errors} when errors != [] ->
        {:error, "Failed to process chunks: #{inspect(errors)}"}

      _ ->
        {:error, "No candidates were imported"}
    end
  end

  defp extract_shareholders_from_past_report(chunk, report_id, custom_prompt, is_uk) do
    case ai_parsing(chunk, :get_from_past_report, custom_prompt, is_uk) do
      {:ok, ai_content} ->
        notify_pub_sub(report_id, {:processed_chunk, 1})
        ai_content["registered_holders"] || []

      {:error, _} ->
        notify_pub_sub(report_id, {:processed_chunk, 1})
        []
    end
  end

  defp extract_shareholders(chunk, file_type, report_id, custom_prompt, is_uk) do
    case ai_parsing(chunk, file_type, custom_prompt, is_uk) do
      {:ok, ai_content} ->
        notify_pub_sub(report_id, {:processed_chunk, 1})
        ai_content["shareholders"] || []

      {:error, _} ->
        notify_pub_sub(report_id, {:processed_chunk, 1})
        []
    end
  end

  defp ai_parsing_headers(content, file_type, custom_prompt) do
    ai_parsing(content, file_type, custom_prompt, false)
  end

  defp merge_candidates(existing, new_candidate) do
    existing_shares = maybe_process_shares(existing["shares"])
    new_shares = maybe_process_shares(new_candidate["shares"])
    Map.put(existing, "shares", to_string(existing_shares + new_shares))
  end

  defp merge_registered_holders(existing, new_registered_holder) do
    existing_shares = maybe_process_shares(existing["shares"])
    new_shares = maybe_process_shares(new_registered_holder["shares"])
    Map.put(existing, "shares", to_string(existing_shares + new_shares))
    existing_beneficial_owners = existing["beneficial_owners"] || []
    new_beneficial_owners = new_registered_holder["beneficial_owners"] || []
    Map.put(existing, "beneficial_owners", existing_beneficial_owners ++ new_beneficial_owners)
  end

  defp import_candidate_chunk(chunk, report_id, parent_id) do
    case import_candidates(chunk, report_id, parent_id) do
      {:ok, result} -> {:ok, result}
      {:error, error} -> {:error, "Error processing chunk: #{inspect(error)}"}
    end
  end

  defp accumulate_results({:ok, {:ok, %{candidates: {count, _}}}}, acc) do
    %{acc | total_imported: acc.total_imported + count}
  end

  defp accumulate_results({:ok, {:error, error}}, acc) do
    %{acc | errors: [error | acc.errors]}
  end

  defp accumulate_results({:exit, reason}, acc) do
    %{acc | errors: [reason | acc.errors]}
  end

  defp accumulate_results(other, acc) do
    %{acc | errors: ["Unhandled result format: #{inspect(other)}" | acc.errors]}
  end

  defp format_cell(nil), do: ""
  defp format_cell(val) when is_float(val), do: :erlang.float_to_binary(val, decimals: 1)
  defp format_cell(val), do: "#{val}"

  def extract_upload_data(upload, password, file_type) do
    case upload do
      %Plug.Upload{path: path} ->
        maybe_read_file_with_password(path, password, file_type)

      _ ->
        {:error, "Invalid upload format"}
    end
  end

  @spec ai_parsing(String.t(), atom(), String.t(), boolean()) :: {:ok, map()} | {:error, String.t()}
  def ai_parsing(content, file_type, custom_prompt, is_uk) do
    ai_parsing_impl().(content, file_type, custom_prompt, is_uk)
  end

  defp import_past_report_candidate(registered_holder, report_id) do
    case BeneficialOwners.insert_past_report_candidate(registered_holder, report_id) do
      {:ok, result} ->
        {:ok, result}

      {:error, error} ->
        {:error, "Error importing candidates: #{inspect(error)}"}
    end
  end

  defp import_candidates(candidates, report_id, parent_id) do
    with {:ok, parent} <- get_parent_candidate(parent_id),
         {:ok, result} <- BeneficialOwners.upsert_candidates_multi(candidates, parent, report_id) do
      {:ok, result}
    else
      {:error, error} ->
        {:error, "Error importing candidates: #{inspect(error)}"}
    end
  end

  defp get_parent_candidate(parent_id) do
    case BeneficialOwners.get_report_candidate!(parent_id) do
      nil -> {:error, "Parent candidate not found"}
      parent -> {:ok, parent}
    end
  end

  defp random_string(length \\ 5) do
    length
    |> :crypto.strong_rand_bytes()
    |> Base.url_encode64()
    |> binary_part(0, length)
  end

  def maybe_read_file_with_password(path, password, file_type) do
    cond do
      password == "" ->
        read_file(path, file_type)

      file_type not in [:pdf, :xlsx, :xls] and password != "" ->
        {:error, "Password is not supported for this file type"}

      true ->
        folder = "/tmp/beneficial_owners_upload"
        File.mkdir_p!(folder)
        temp_file_path = "#{folder}/#{random_string()}.#{file_type}"

        try do
          case run_decrypt_file_command(path, password, temp_file_path, file_type) do
            :ok ->
              read_file(temp_file_path, file_type)

            {:error, error} ->
              Logger.error("Error decrypting file: #{inspect(error)}")
              read_file(path, file_type)
          end
        rescue
          e ->
            {:error, "Error decrypting file: #{inspect(e)}"}
        after
          File.rm_rf!(folder)
        end
    end
  end

  defp read_file(path, :pdf) do
    with {:ok, first_page_content} <- get_pdf_first_page_content(path),
         {:ok, chunked_content} <- get_pdf_chunked_content(path) do
      {:ok, {first_page_content, chunked_content}}
    else
      {:error, error} ->
        {:error, "Error reading file: #{inspect(error)}"}
    end
  end

  defp read_file(path, :xls) do
    # convert xls to xlsx
    xlsx_path =
      if String.ends_with?(path, ".xls") do
        String.replace(path, ".xls", ".xlsx")
      else
        path <> ".xlsx"
      end

    case System.cmd("ssconvert", [path, xlsx_path]) do
      {_, 0} ->
        read_file(xlsx_path, :xlsx)

      {_, _} ->
        {:error, "Error converting xls to xlsx"}
    end
  end

  defp read_file(path, _file_type) do
    case File.read(path) do
      {:ok, content} ->
        {:ok, content}

      {:error, reason} ->
        {:error, "Failed to read file: #{inspect(reason)}"}
    end
  end

  defp get_pdf_first_page_content(path) do
    folder = "/tmp/beneficial_owners_upload_first_page"
    File.mkdir_p!(folder)
    temp_file_path = "#{folder}/#{random_string()}.pdf"

    try do
      case System.cmd("qpdf", [path, "--pages", path, "1", "--", temp_file_path]) do
        {_, exit_code} when exit_code in [0, 3] ->
          case File.read(temp_file_path) do
            {:ok, content} ->
              {:ok, Base.encode64(content)}

            {:error, reason} ->
              {:error, "Failed to read extracted page: #{inspect(reason)}"}
          end

        {output, exit_code} ->
          {:error, "QPDF failed with exit code #{exit_code}: #{output}"}
      end
    rescue
      e ->
        {:error, "Unexpected error: #{inspect(e)}"}
    after
      File.rm_rf!(folder)
    end
  end

  defp get_pdf_chunked_content(path) do
    folder = "/tmp/beneficial_owners_upload_chunked"
    File.mkdir_p!(folder)
    chunks_path_prefix = "#{folder}/chunk-#{random_string()}/"
    File.mkdir_p!(chunks_path_prefix)

    try do
      {pages_output, 0} = System.cmd("qpdf", ["--show-npages", path])
      page_count = pages_output |> String.trim() |> String.to_integer()

      ranges = calculate_overlapping_ranges(page_count, @pdf_chunk_size)

      chunk_files =
        Enum.map(ranges, fn {start_page, end_page, index} ->
          output_file = "#{chunks_path_prefix}c-#{index}.pdf"

          # Extract the range of pages for this chunk
          {output, exit_code} =
            System.cmd(
              "qpdf",
              [
                path,
                "--pages",
                path,
                "--range=#{start_page}-#{end_page}",
                "--",
                output_file
              ],
              stderr_to_stdout: true
            )

          # Accept both 0 and 3 as success codes (3 means warnings but succeeded)
          if exit_code in [0, 3] do
            output_file
          else
            raise "QPDF failed with exit code #{exit_code}: #{output}"
          end
        end)

      chunks =
        Enum.map(chunk_files, fn file ->
          content = File.read!(file)
          Base.encode64(content)
        end)

      {:ok, chunks}
    rescue
      e in File.Error -> {:error, e.reason}
      e -> {:error, "Failed to chunk PDF: #{inspect(e)}"}
    after
      File.rm_rf!(folder)
    end
  end

  defp run_decrypt_file_command(path, password, temp_file_path, :pdf) do
    case System.cmd("qpdf", ["--password=#{password}", "--decrypt", path, temp_file_path]) do
      {_, output_code} when output_code in [0, 3] ->
        :ok

      {output, _} ->
        {:error, "PDF file decryption failed with error: #{inspect(output)}"}
    end
  end

  defp run_decrypt_file_command(path, password, temp_file_path, file_type) when file_type in [:xlsx, :xls] do
    case System.cmd("msoffcrypto-tool", ["-p", password, path, temp_file_path]) do
      {_, 0} ->
        :ok

      {output, _} ->
        {:error, "Excel file decryption failed with error: #{inspect(output)}"}
    end
  end

  defp maybe_process_shares(shares) when shares in [nil, ""] do
    0
  end

  defp maybe_process_shares(shares) when is_binary(shares) do
    shares
    |> String.replace(",", "")
    |> String.to_integer()
  end

  defp maybe_process_shares(shares) when is_integer(shares), do: shares

  defp notify_pub_sub(report_id, message) do
    Phoenix.PubSub.broadcast(
      Gaia.PubSub,
      "bo_report_candidates_#{report_id}",
      {:processing_document_update, message}
    )
  end

  defp calculate_overlapping_ranges(total_pages, chunk_size) do
    # Generate ranges with overlapping pages
    0..(ceil((total_pages - 1) / (chunk_size - 1)) - 1)
    |> Enum.map(fn i ->
      start_page = i * (chunk_size - 1) + 1
      end_page = min(start_page + chunk_size - 1, total_pages)
      if start_page <= total_pages, do: {start_page, end_page, i + 1}
    end)
    |> Enum.filter(& &1)
  end

  def encode_password(password) do
    encryption_key = Application.get_env(:gaia, :beneficial_owner_nominee_contacts_encryption_key)

    iv = :crypto.strong_rand_bytes(16)
    # Pad the password to ensure it's a multiple of 16 bytes (AES block size)
    padded_password = pad_pkcs7(password)
    encrypted = :crypto.crypto_one_time(:aes_256_cbc, encryption_key, iv, padded_password, true)
    Base.encode64(iv <> encrypted)
  end

  def decode_password(encoded_password) do
    encryption_key = Application.get_env(:gaia, :beneficial_owner_nominee_contacts_encryption_key)
    decoded = Base.decode64!(encoded_password)
    <<iv::binary-16, encrypted::binary>> = decoded
    decrypted = :crypto.crypto_one_time(:aes_256_cbc, encryption_key, iv, encrypted, false)
    # Remove padding
    unpad_pkcs7(decrypted)
  rescue
    e ->
      Logger.error("Error decoding password: #{inspect(e)}", %{
        "encoded_password" => encoded_password
      })

      ""
  end

  # Helper function to add PKCS#7 padding
  defp pad_pkcs7(data) do
    block_size = 16
    padding_size = block_size - rem(byte_size(data), block_size)
    data <> :binary.copy(<<padding_size>>, padding_size)
  end

  # Helper function to remove PKCS#7 padding
  defp unpad_pkcs7(data) do
    padding_size = :binary.last(data)
    binary_part(data, 0, byte_size(data) - padding_size)
  end
end
