defmodule Gaia.BeneficialOwners.CandidateImporterAiParser do
  @moduledoc """
  Parser for the candidate importer AI.
  """

  @behaviour Gaia.BeneficialOwners.CandidateImporterAi

  alias LangChain.Chains.LLMChain
  alias LangChain.ChatModels.ChatOpenAI
  alias LangChain.Message
  alias LangChain.Message.ContentPart

  def ai_parsing(content, file_type, custom_prompt, is_uk) do
    system_prompt = get_system_prompt(file_type, custom_prompt, is_uk)
    user_message = get_user_message(content, file_type)

    case %{llm: ChatOpenAI.new!(%{model: "gpt-4o"})}
         |> LLMChain.new!()
         |> LLMChain.add_message(Message.new_system!(system_prompt))
         |> LLMChain.add_message(user_message)
         |> LLMChain.run() do
      {:ok, %LangChain.Chains.LLMChain{} = chain} ->
        parse_chain_response(chain)

      {:error, _chain, %LangChain.LangChainError{} = error} ->
        {:error, "Failed to parse given #{file_type} file: #{inspect(error)}"}
    end
  end

  defp get_system_prompt(:get_from_past_report, custom_prompt, is_uk) do
    """
    You are an expert financial data analyst. You are given a past report file that contains the information of the shareholders/beneficial owners, their shares, their address/country, and their registered holder.
    You must extract the information of the shareholders/beneficial owners from the document, ignore the irrelevant information, and return the information in a json format.
    A beneficial owner may or may not belongs to a registered holder, if it belongs to the registered holder, please include the beneficial owner as a child of the registered holder.

    The json format should be as follows:
    {
      "registered_holders": [
        {
          "account_name": "Registered Holder Name",
          "beneficial_owners": [
            {
              "account_name": "Beneficial Owner Name",
               "shares": "Number of shares held (integer)",
                "address_line_one": "Address line one",
                "address_line_two": "Address line two",
                "address_city": "City",
                "address_state": "State",
                "address_postcode": "Postcode",
                "address_country": "Country"#{if is_uk, do: ",\n\"depot\": \"Depot/Designation code\""}
            }, ...]
          "shares": "Number of shares for the registered holder (integer)",
          "address_line_one": "Address line one",
          "address_line_two": "Address line two",
          "address_city": "City",
          "address_state": "State",
          "address_postcode": "Postcode",
          "address_country": "Country"#{if is_uk, do: ",\n\"depot\": \"Depot/Designation code\""}
        },
        ...
      ]
    }

    If some of the information is not available, please leave it an empty string (i.e. "address_line_two": "").
    DO NOT CHANGE THE VALUE OF ORIGINAL DATA.
    DO NOT ADD ANY NEW DATA THAT IS NOT IN THE FILE.

    #{custom_prompt}

    Return ONLY the JSON object (i.e. {"registered_holders": []}), with no additional text or explanation.
    """
  end

  defp get_system_prompt(:get_headers, _, _) do
    """
    You are an expert financial data analyst. You are given a shareholding disclosure table that contains the headers of the table.
    You must extract the header row of the table, normally it is one of the first few rows of the table and contains information such as account name, shares, address, etc.
    JUST RETURN THE HEADER ROW, DO NOT RETURN ANY OTHER TEXT.

    The json format should be as follows:
    {
      "headers": ["Column 1", "Column 2", "Column 3", ...]
    }
    """
  end

  defp get_system_prompt(:get_headers_pdf, _, _) do
    """
    You are an expert financial data analyst. You are given the first page of the shareholding disclosure PDF that contains the headers of a table formatted in the pdf.
    You must extract the header row of the table, normally it is one of the first few rows of the table and contains information such as shareholder name, shares, address, etc.
    JUST RETURN THE HEADER ROW, DO NOT RETURN ANY OTHER TEXT.
    YOU MUST RETURN IN THE JSON FORMAT.

    The json format should be as follows:
    {
      "headers": ["Column 1", "Column 2", ...]
    }
    """
  end

  defp get_system_prompt(:pdf, custom_prompt, is_uk) do
    """
    You are an expert financial data analyst. You are given a shareholding disclosure document in PDF format.
    You must extract the information of the shareholders from the document, ignore the irrelevant information, and return the information in a json format.

    The json format should be as follows:
    {
      "shareholders": [
        {
          "account_name": "Shareholder Account Name",
          "shares": "Number of shares held (integer positive or negative)",
          "address_line_one": "Address line one",
          "address_line_two": "Address line two",
          "address_city": "City",
          "address_state": "State",
          "address_postcode": "Postcode",
          "address_country": "Country"#{if is_uk, do: ",\n\"depot\": \"Depot/Designation code\""}
        },
        ....
      ]
    }

    If some of the information is not available, please leave it an empty string (i.e. "address_line_two": "").
    DO NOT CHANGE THE VALUE OF ORIGINAL DATA.
    DO NOT ADD ANY NEW DATA THAT IS NOT IN THE PDF FILE.
    IF YOU ARE NOT SURE ABOUT THE DATA, PLEASE LEAVE IT EMPTY OR IGNORE THE RECORD.

    VALID AU STATE: ACT, NSW, NT, QLD, SA, TAS, VIC, WA
    VALID AU POSTCODE: 0000, 0001, 0002, ..., 9999
    VALID UK POSTCODE consists of 5 to 7 alphanumeric characters, separated by a space, with the following format: [A-Z]{1,2}[0-9][A-Z0-9]? [0-9][A-Z]{2}.

    Return ONLY the JSON object (i.e. {"shareholders": []}), with no additional text or explanation.

    #{custom_prompt}
    """
  end

  defp get_system_prompt(file_type, custom_prompt, is_uk) do
    """
    You are an expert financial data analyst. You are given a shareholding disclosure document in #{file_type} format.
    You must extract the information of the shareholders from the document, ignore the irrelevant information, and return the information in a json format.

    The json format should be as follows:
    {
      "shareholders": [
        {
          "account_name": "Shareholder Name",
          "shares": "Number of shares held (integer positive or negative)",
          "address_line_one": "Address line one",
          "address_line_two": "Address line two",
          "address_city": "City",
          "address_state": "State",
          "address_postcode": "Postcode",
          "address_country": "Country"#{if is_uk, do: ",\n\"depot\": \"Depot/Designation code\""}
        },
        ....
      ]
    }

    If some of the information is not available, please leave it an empty string (i.e. "address_line_two": "").

    DO NOT CHANGE THE VALUE OF ORIGINAL DATA.
    DO NOT ADD ANY NEW DATA THAT IS NOT IN THE PDF FILE.
    IF YOU ARE NOT SURE ABOUT THE DATA, PLEASE LEAVE IT EMPTY OR IGNORE THE RECORD.

    VALID AU STATE: ACT, NSW, NT, QLD, SA, TAS, VIC, WA
    VALID AU POSTCODE: 0000, 0001, 0002, ..., 9999
    VALID UK POSTCODE consists of 5 to 7 alphanumeric characters, separated by a space, with the following format: [A-Z]{1,2}[0-9][A-Z0-9]? [0-9][A-Z]{2}.


    PARSE THE Address line one INTO DETAILS IF IT CONTAINS CITY, STATE, POSTCODE AND COUNTRY.

    IF the headers have account holder 1 and account holder 2, merge them into one record and their account_name should be "Account Holder 1 & Account Holder 2".

    #{custom_prompt}

    Return ONLY the JSON object (i.e. {"shareholders": []}), with no additional text or explanation.
    """
  end

  defp get_user_message(content, :get_from_past_report) do
    Message.new_user!("Here is the content of the past report file: #{content}")
  end

  defp get_user_message(content, :get_headers) do
    Message.new_user!("Here is the table content: #{content}")
  end

  defp get_user_message(content, :get_headers_pdf) do
    Message.new_user!([
      ContentPart.text!("Here is the first page of the shareholding disclosure PDF file:"),
      ContentPart.file!(content,
        type: :base64,
        filename: "shareholding_disclosure_first_page.pdf"
      )
    ])
  end

  defp get_user_message({headers, content}, :pdf) do
    Message.new_user!([
      ContentPart.text!("Here is the content of the shareholding disclosure PDF file, the headers are: #{headers}"),
      ContentPart.file!(content,
        type: :base64,
        filename: "shareholding_disclosure.pdf"
      )
    ])
  end

  defp get_user_message(content, file_type) do
    Message.new_user!("""
    Here is the content of the shareholding disclosure #{file_type} file:
    #{content}
    """)
  end

  defp parse_chain_response(chain) do
    case List.last(chain.messages) do
      %LangChain.Message{content: content} when not is_nil(content) ->
        cleaned_content =
          content
          |> String.replace(~r/```json\n/, "")
          |> String.replace(~r/```$/, "")

        case Jason.decode(cleaned_content) do
          {:ok, json_content} ->
            {:ok, json_content}

          {:error, _} ->
            {:error, "Failed to parse the response. The response was not valid JSON."}
        end

      _ ->
        {:error, "No response received from AI model"}
    end
  end
end
