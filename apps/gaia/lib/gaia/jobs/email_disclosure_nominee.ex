defmodule Gaia.Jobs.EmailDisclosureNominee do
  @moduledoc """
  This job is used to send a disclosure email to unmask nominees
  """

  use Oban.Worker, queue: :default, max_attempts: 3, priority: 0

  import Swoosh.Email

  alias Gaia.BeneficialOwners
  alias Gaia.Companies
  alias Gaia.Companies.Profile

  require Helper.Error.Custom.ErrorHandler
  require Logger

  def enqueue(args, opts \\ []) do
    args
    |> new(opts)
    |> Oban.insert()
  end

  @impl Oban.Worker
  def perform(%Oban.Job{
        args: %{
          "company" => %{
            "profile_id" => company_profile_id,
            "acn" => company_acn,
            "sedol" => company_sedol,
            "isin" => company_isin
          },
          "authorisation_letter_id" => authorisation_letter_id,
          "schedule_date" => schedule_date,
          "deadline_date" => deadline_date,
          "request_holding_date" => request_holding_date,
          "candidate" => %{
            "id" => candidate_id,
            "name" => nominee_name,
            "email" => nominee_email,
            "cc_emails" => cc_emails,
            "account_name" => nominee_account_name,
            "address" => %{
              "line_one" => address_one,
              "line_two" => address_two,
              "city" => city,
              "state" => state,
              "postcode" => postcode,
              "country" => country
            }
          },
          "sender_name" => sender_name
        }
      }) do
    Logger.info("Gaia.Jobs.EmailDisclosureNominee started.")

    address =
      format_address(
        address_one,
        address_two,
        city,
        state,
        postcode,
        country
      )

    with {_, %Profile{} = company_profile} <-
           {:get_profile, Companies.get_profile_with_preload(company_profile_id, [:ticker])},
         {:ok, schedule_date} <- Date.from_iso8601(schedule_date),
         {:ok, deadline_date} <- parse_deadline_date(deadline_date, schedule_date),
         {:ok, request_holding_date} <- Date.from_iso8601(request_holding_date),
         authorisation_letter =
           BeneficialOwners.get_authorisation_letter!(authorisation_letter_id),
         true <- Date.after?(authorisation_letter.expired_date, Date.utc_today()),
         true <- Date.after?(deadline_date, schedule_date),
         true <- authorisation_letter.company_profile_id == company_profile_id,
         letter_file_url =
           Gaia.Uploaders.BeneficialOwners.AuthorisationLetter.url(
             {authorisation_letter.file, authorisation_letter},
             signed: true
           ),
         request_holding_date =
           Calendar.strftime(
             request_holding_date,
             "%-d#{ordinal_suffix(request_holding_date.day)} of %B %Y"
           ),
         letter_date =
           Calendar.strftime(schedule_date, "%-d#{ordinal_suffix(schedule_date.day)} of %B %Y"),
         response_deadline =
           Calendar.strftime(deadline_date, "%-d#{ordinal_suffix(deadline_date.day)} of %B %Y"),
         email_args =
           prepare_email_args(%{
             company_profile: company_profile,
             nominee_name: nominee_name,
             nominee_address: address,
             letter_date: letter_date,
             response_deadline: response_deadline,
             sender_name: sender_name,
             company_acn: company_acn,
             company_isin: company_isin,
             company_sedol: company_sedol,
             nominee_account_name: nominee_account_name,
             loa_date: Calendar.strftime(authorisation_letter.inserted_at, "%d%m%y"),
             request_holding_date: request_holding_date
           }),
         {:ok, html_content} <-
           BeneficialOwners.construct_template_html(
             email_args.disclosure_letter_type,
             email_args.disclosure_letter_args
           ),
         {:ok, email_html} <-
           BeneficialOwners.construct_template_html(
             email_args.disclosure_request_email_type,
             email_args.disclosure_request_email_args
           ),
         uniq_file_name = "#{Ecto.UUID.generate()}_#{company_profile_id}",
         {:ok, disclosure_pdf_path} <-
           PdfGenerator.generate(html_content,
             output: "/tmp/#{uniq_file_name}.pdf",
             timeout: 30_000
           ),
         {:ok, %{id: email_id}} <-
           prepare_and_send_email(
             nominee_email,
             cc_emails,
             email_html,
             disclosure_pdf_path,
             letter_file_url,
             email_args
           ),
         File.rm!(disclosure_pdf_path),
         {:ok, %{id: _tracking_email_id}} <-
           create_tracking_email_details(%{
             module_name: "Gaia.Jobs.EmailDisclosureNominee",
             function_name: "prepare_and_send_email",
             email_id: email_id,
             from: "<EMAIL>",
             to: nominee_email,
             html_body: email_html,
             subject: email_args.subject,
             company_profile_id: company_profile_id,
             source: :transactional
           }),
         {:ok, _} <-
           candidate_id
           |> BeneficialOwners.get_report_candidate!()
           |> BeneficialOwners.update_report_candidate(%{last_contact_at: DateTime.utc_now()}) do
      Logger.info("Gaia.Jobs.EmailDisclosureNominee completed.")
    else
      false ->
        {:discard, "Authorisation letter unauthorised or expired"}

      {:error, error} ->
        Helper.Error.Custom.ErrorHandler.handle_error(
          "Error on sending and tracking disclosure email to nominee",
          %{
            nominee_email: nominee_email,
            error_details: inspect(error)
          },
          %RuntimeError{message: inspect(error)}
        )

        {:error, error}
    end
  end

  def perform(_), do: {:error, "Unable to send a disclosure email to unmask nominees"}

  defp prepare_and_send_email(nominee_email, cc_emails, email_html, disclosure_pdf_path, letter_file_url, email_args) do
    with {:ok, authorisation_letter_content} <- fetch_file_from_url(letter_file_url),
         {:ok, result} <-
           %{
             recipient_email: nominee_email,
             cc_emails: cc_emails || [],
             email_html: email_html,
             disclosure_pdf_path: disclosure_pdf_path,
             auth_letter_content: authorisation_letter_content,
             email_args: email_args
           }
           |> prepare_email_for_nominee()
           |> EmailMarketing.Mailer.deliver() do
      {:ok, result}
    else
      {:error, error} ->
        {:error, "Error on sending disclosure email to nominee: #{inspect(error)}"}
    end
  end

  defp prepare_email_for_nominee(%{
         recipient_email: recipient_email,
         cc_emails: cc_emails,
         email_html: email_html,
         disclosure_pdf_path: disclosure_pdf_path,
         auth_letter_content: authorisation_letter_content,
         email_args: email_args
       }) do
    # Filter and clean CC emails (don't include the sender email in CC)
    filtered_cc_emails =
      cc_emails
      |> Enum.filter(&(&1 != nil and &1 != ""))
      |> Enum.uniq()

    email =
      new()
      |> to(recipient_email)
      |> cc(email_args.contact_email)
      |> reply_to(email_args.contact_email)
      |> from({email_args.contact_name, email_args.contact_email})
      |> subject(email_args.subject)
      |> html_body(email_html)
      |> attachment(
        Swoosh.Attachment.new(disclosure_pdf_path,
          filename: email_args.disclosure_letter_name,
          content_type: "application/pdf",
          type: :attachment
        )
      )
      |> attachment(
        Swoosh.Attachment.new({:data, authorisation_letter_content},
          filename: email_args.loa_name,
          content_type: "application/pdf",
          type: :attachment
        )
      )

    # Add CC emails if any exist
    if Enum.empty?(filtered_cc_emails) do
      email
    else
      Enum.reduce(filtered_cc_emails, email, fn cc_email, acc ->
        cc(acc, cc_email)
      end)
    end
  end

  defp fetch_file_from_url(url) do
    case HTTPoison.get(url) do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        {:ok, body}

      {:ok, %HTTPoison.Response{status_code: status_code}} ->
        {:error, "Failed to download file, status code: #{status_code}"}

      {:error, %HTTPoison.Error{reason: reason}} ->
        {:error, "Error downloading file: #{inspect(reason)}"}
    end
  end

  defp ordinal_suffix(day) do
    cond do
      day in [11, 12, 13] -> "th"
      rem(day, 10) == 1 -> "st"
      rem(day, 10) == 2 -> "nd"
      rem(day, 10) == 3 -> "rd"
      true -> "th"
    end
  end

  defp parse_deadline_date(date_string, _) when is_binary(date_string) and date_string != "",
    do: Date.from_iso8601(date_string)

  defp parse_deadline_date(_, base_date), do: {:ok, Date.add(base_date, 2)}

  defp format_address(line_one, line_two, city, state, postcode, country) do
    city_state_postcode =
      [city, state, postcode]
      |> Enum.filter(fn x -> x && x != "" end)
      |> Enum.join(" ")

    [line_one, line_two, city_state_postcode, country]
    |> Enum.filter(fn x -> x && x != "" end)
    |> Enum.map_join("<br/>", &String.upcase/1)
  end

  # Add tracking email details into db,
  defp create_tracking_email_details(args) do
    Gaia.Tracking.create_email(args)
  end

  # Prepare email args for au and uk companies
  defp prepare_email_args(%{
         company_profile: company_profile,
         nominee_name: nominee_name,
         nominee_address: address,
         letter_date: letter_date,
         response_deadline: response_deadline,
         sender_name: sender_name,
         company_acn: company_acn,
         company_isin: company_isin,
         company_sedol: company_sedol,
         nominee_account_name: nominee_account_name,
         loa_date: loa_date,
         request_holding_date: request_holding_date
       }) do
    if company_profile.ticker.market_key in [:lse, :aqse] do
      %{
        disclosure_letter_type: :disclosure_letter_uk,
        disclosure_request_email_type: :disclosure_request_email_uk,
        disclosure_letter_args: %{
          recipient_name: nominee_name,
          authorised_company: company_profile.trading_name,
          isin: company_isin,
          sedol: format_sedol(company_sedol, :letter),
          letter_date: letter_date,
          request_holding_date: request_holding_date
        },
        disclosure_request_email_args: %{
          authorised_company:
            company_profile.trading_name <>
              " (ISIN: " <> company_isin <> ")" <> format_sedol(company_sedol, :email),
          request_holding_date: request_holding_date
        },
        contact_email: "<EMAIL>",
        contact_name: "s793 Reports Team",
        subject: "Nominee Section 793 Notice [#{nominee_account_name}] - #{company_profile.trading_name}",
        loa_name: "Letter of Authorisation #{company_profile.trading_name} - #{loa_date}.pdf",
        disclosure_letter_name: "#{nominee_account_name} - Section 793 Notice for #{company_profile.trading_name}.pdf"
      }
    else
      %{
        disclosure_letter_type: :disclosure_letter,
        disclosure_request_email_type: :disclosure_request_email,
        disclosure_letter_args: %{
          recipient_name: nominee_name,
          recipient_address: address,
          recipient_account_name: nominee_account_name,
          authorised_company: company_profile.trading_name <> " (ACN: " <> company_acn <> ")",
          letter_date: letter_date,
          request_holding_date: request_holding_date
        },
        disclosure_request_email_args: %{
          client_trading_name: company_profile.trading_name,
          client_acn: company_acn,
          client_isin: company_isin,
          response_deadline: response_deadline,
          request_holding_date: request_holding_date,
          sender_name: sender_name
        },
        contact_email: "<EMAIL>",
        contact_name: "Disclosures Team",
        subject:
          "Corporations Act 2001 s 672A Disclosure Request [#{nominee_account_name}] - #{company_profile.trading_name}",
        loa_name: "Letter of Authorisation #{String.upcase(company_profile.trading_name)} - #{loa_date}.pdf",
        disclosure_letter_name:
          "#{nominee_account_name} - Section 672A Disclosure Request for #{company_profile.trading_name}.pdf"
      }
    end
  end

  defp format_sedol("", _), do: ""
  defp format_sedol(nil, _), do: ""

  defp format_sedol(sedol, :email) do
    ", (SEDOL: " <> sedol <> ")"
  end

  defp format_sedol(sedol, :letter) do
    " SEDOL: " <> sedol <> ""
  end
end
