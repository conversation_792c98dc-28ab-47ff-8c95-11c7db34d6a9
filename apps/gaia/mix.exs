defmodule Gaia.MixProject do
  use Mix.Project

  def project do
    [
      app: :gaia,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      deps: deps(),
      aliases: aliases(),
      preferred_cli_env: [
        "test.ecto.reset": :test
      ]
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      included_applications: [:fun_with_flags],
      extra_applications: [:logger],
      mod: {Gaia.Application, []}
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:amazon_web_service, in_umbrella: true},
      {:arc_ecto, in_umbrella: true},
      {:arc_gcs, "~> 0.2"},
      {:arc, "~> 0.11.0"},
      {:automic, in_umbrella: true},
      {:bcrypt_elixir, "~> 2.0"},
      {:boardroom, in_umbrella: true},
      {:chromic_pdf, "~> 1.14"},
      {:cloak_ecto, "~> 1.2.0"},
      {:cloudex, "~> 1.4.1"},
      {:computershare, in_umbrella: true},
      {:countries, "~> 1.6"},
      {:css_colors, "~> 0.2.2"},
      {:date_time_parser, "~> 1.1.2"},
      {:ecto_erd, "~> 0.4", only: :dev, runtime: false},
      {:ecto_sql, "~> 3.8"},
      {:email_transactional, in_umbrella: true},
      {:email_marketing, in_umbrella: true},
      {:encrypted_secrets, "~> 0.3.0"},
      {:elixir_math, "~> 0.1.0"},
      {:faker, "~> 0.17"},
      {:floki, ">= 0.30.0"},
      {:fun_with_flags, "~> 1.13.0"},
      {:googleapi, in_umbrella: true},
      {:google_api_secret_manager, "~> 0.19"},
      {:google_api_big_query, "~> 0.76.0"},
      {:goth, "~> 1.0"},
      {:hashids, "~> 2.0"},
      {:hackney, "~> 1.24"},
      {:helper, in_umbrella: true},
      {:httpoison, "~> 1.8"},
      {:hubspot, in_umbrella: true},
      {:jason, "~> 1.2"},
      {:link, in_umbrella: true},
      {:neville, in_umbrella: true},
      {:nimble_csv, "~> 1.1"},
      {:oban, "~> 2.12"},
      {:oban_met, "~> 1.0"},
      {:openai, in_umbrella: true},
      {:osm, in_umbrella: true},
      {:pdf_generator, "~> 0.6.2"},
      {:pgvector, "~> 0.3.0"},
      {:phoenix_pubsub, "~> 2.0"},
      {:phoenix, "~> 1.7"},
      {:postgrex, ">= 0.0.0"},
      {:pythia, in_umbrella: true},
      {:quote_media, in_umbrella: true},
      {:refinitiv, in_umbrella: true},
      {:scholar, "~> 0.2.1"},
      {:scrivener_ecto, "~> 2.0"},
      {:sentry, "~> 8.0"},
      {:scraper, in_umbrella: true},
      {:slack, in_umbrella: true},
      {:sweet_xml, "~> 0.7.1"},
      {:tesla, "~> 1.4"},
      {:timex, "~> 3.7.2"},
      {:twilio, in_umbrella: true},
      {:langchain, "~> 0.3.3"},
      {:typed_struct, "~> 0.3.0"},
      {:tzdata, "~> 1.1.2"},
      {:vercel, in_umbrella: true},
      {:weblink, in_umbrella: true},
      {:xcend, in_umbrella: true},
      {:xlsx_reader, "~> 0.4.0"},
      # This one hasn't had a release in years, but a few recent updates
      {:xlsxir, github: "jsonkenl/xlsxir", ref: "4dbebf2eec01ee661d21d78d3ad5b70a91c3339a"},
      {:email_checker, "~> 0.2.4"},
      {:mock, "~> 0.3.0", only: :test},
      {:mox, "~> 1.0", only: :test},
      {:uuid, "~> 1.1"},
      {:yajwt, "~> 1.0"},
      {:slugify, "~> 1.3"},
      {:html_entities, "~> 0.5"},
      {:libcluster, "~> 3.5"}
    ]
  end

  defp aliases do
    [
      "ecto.setup": ["ecto.create", "ecto.migrate"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      "test.ecto.reset": ["ecto.drop --quiet", "ecto.create --quiet", "ecto.migrate --quiet"]
    ]
  end
end
