defmodule Gaia.Repo.Migrations.AddDepotToBo do
  use Ecto.Migration

  def up do
    alter table(:beneficial_owner_report_candidates) do
      add :depot, :string
    end

    alter table(:beneficial_owner_report_participants) do
      add :depot, :string
    end

    alter table(:beneficial_owner_accounts) do
      add :depot, :string
    end

    execute "DROP INDEX IF EXISTS beneficial_owner_report_candidates_unique_idx"
    execute "DROP INDEX IF EXISTS beneficial_owner_report_participants_unique_idx"

    execute "CREATE UNIQUE INDEX beneficial_owner_report_candidates_unique_idx ON beneficial_owner_report_candidates (report_id, account_name, COALESCE(depot, ''), COALESCE(address_line_one, ''), COALESCE(address_postcode, ''), COALESCE(address_country, ''), COALESCE(address_city, ''), parent_id, layer)"
    execute "CREATE UNIQUE INDEX beneficial_owner_report_participants_unique_idx ON beneficial_owner_report_participants (report_id, account_name, COALESCE(depot, ''), COALESCE(address_line_one, ''), COALESCE(address_postcode, ''), COALESCE(address_country, ''), COALESCE(address_city, ''), parent_id, layer)"

  end

  def down do
    alter table(:beneficial_owner_report_candidates) do
      remove :depot
    end

    alter table(:beneficial_owner_report_participants) do
      remove :depot
    end

    alter table(:beneficial_owner_accounts) do
      remove :depot
    end

    execute "DROP INDEX IF EXISTS beneficial_owner_report_candidates_unique_idx"
    execute "DROP INDEX IF EXISTS beneficial_owner_report_participants_unique_idx"

    execute "CREATE UNIQUE INDEX beneficial_owner_report_candidates_unique_idx ON beneficial_owner_report_candidates (report_id, account_name, COALESCE(address_line_one, ''), COALESCE(address_postcode, ''), parent_id, layer)"
    execute "CREATE UNIQUE INDEX beneficial_owner_report_participants_unique_idx ON beneficial_owner_report_participants (report_id, account_name, COALESCE(address_line_one, ''), COALESCE(address_postcode, ''), parent_id, layer)"
  end
end
