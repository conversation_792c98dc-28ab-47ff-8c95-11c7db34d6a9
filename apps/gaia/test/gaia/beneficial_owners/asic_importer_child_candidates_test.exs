defmodule Gaia.BeneficialOwners.AsicImporterChildCandidatesTest do
  use Gaia.DataCase, async: true

  alias Gaia.BeneficialOwners.AsicImporter
  alias Gaia.BeneficialOwners.AsicMember
  alias Gaia.BeneficialOwners.AsicMemberShareStructure
  alias Gaia.BeneficialOwners.AsicOrganisation
  alias Gaia.BeneficialOwners.AsicShareStructure
  alias Gaia.BeneficialOwners.Report
  alias Gaia.BeneficialOwners.ReportCandidate
  alias Gaia.Companies.Profile
  alias Gaia.Repo

  test "create_child_candidates upserts candidates with on_conflict and unsafe_fragment" do
    # Setup: create a Profile, Report, Organisation, ShareStructures, and a parent candidate
    profile =
      Repo.insert!(%Profile{
        name: "Test Co",
        trading_name: "Test Co",
        timezone: "Australia/Sydney"
      })

    report =
      Repo.insert!(%Report{
        company_profile_id: profile.id,
        type: :completed,
        report_date: ~D[2024-01-01]
      })

    org =
      Repo.insert!(%AsicOrganisation{
        company_name: "Test Org",
        acn: "*********"
      })

    share_structure =
      Repo.insert!(%AsicShareStructure{
        class: "A",
        number_issued: 1000,
        asic_organisation_id: org.id
      })

    parent =
      Repo.insert!(%ReportCandidate{
        report_id: report.id,
        account_name: "Parent",
        depot: nil,
        address_line_one: "Parent St",
        address_postcode: "1000",
        address_country: "AU",
        address_city: "Sydney",
        parent_id: nil,
        layer: 0,
        shares: 1000
      })

    member =
      Repo.insert!(%AsicMember{
        name: "Child 1",
        address_line_one: "Child St",
        address_city: "Sydney",
        address_postcode: "1000",
        address_country: "AU",
        asic_organisation_id: org.id
      })

    _amss =
      Repo.insert!(%AsicMemberShareStructure{
        asic_member_id: member.id,
        asic_share_structure_id: share_structure.id,
        number_held: Decimal.new(100),
        beneficially_held: true,
        document_number: "doc1"
      })

    share_structures = [share_structure]
    members = [member]

    # First call: should insert
    {:ok, [candidate1]} =
      AsicImporter.create_child_candidates(Repo, parent, share_structures, members)

    assert candidate1.account_name == "Child 1"
    assert candidate1.shares > 0

    # Second call: should upsert (trigger on_conflict)
    {:ok, [candidate2]} =
      AsicImporter.create_child_candidates(Repo, parent, share_structures, members)

    assert candidate2.id == candidate1.id
    assert candidate2.shares == candidate1.shares
  end
end
