defmodule Gaia.BeneficialOwners.CandidateImporterAiTest do
  use Gaia.DataCase, async: false

  import Mox

  alias Gaia.BeneficialOwners.CandidateImporterAi
  alias Gaia.BeneficialOwners.CandidateImporterAiMock

  setup :verify_on_exit!

  @csv_file "sample.csv"
  @xlsx_file "sample.xlsx"

  describe "candidate importer with AI" do
    setup [:prepare_csv_file, :prepare_xlsx_file]

    test "read file without password", %{xlsx_file: xlsx_file} do
      assert {:ok, _file_content} =
               CandidateImporterAi.extract_upload_data(
                 xlsx_file,
                 "",
                 :xlsx
               )
    end

    test "chunk_file_content with csv file", %{csv_file: csv_file} do
      assert {:ok, file_content} =
               CandidateImporterAi.extract_upload_data(
                 csv_file,
                 "",
                 :csv
               )

      expect(CandidateImporterAiMock, :ai_parsing, fn _content, _type, _custom_prompt, _is_uk ->
        {:ok,
         %{
           "headers" => [
             "Client Name",
             "Client Add1",
             "Client Add2",
             "Client Add3",
             "Client Add4",
             "Client Add5",
             "Client Postcode",
             "Brand",
             "Stock Code",
             "Stock Desc",
             "Branch Code",
             "Free Qty"
           ]
         }}
      end)

      {:ok, chunks} = CandidateImporterAi.chunk_file_content(file_content, :csv)
      assert length(chunks) == 2
    end

    defp prepare_csv_file(_) do
      file_path = Path.expand("files/#{@csv_file}", __DIR__)

      csv_file = %Plug.Upload{
        path: file_path,
        filename: @csv_file,
        content_type: "text/csv"
      }

      [csv_file: csv_file]
    end

    defp prepare_xlsx_file(_) do
      file_path = Path.expand("files/#{@xlsx_file}", __DIR__)

      xlsx_file = %Plug.Upload{
        path: file_path,
        filename: @xlsx_file,
        content_type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      }

      [xlsx_file: xlsx_file]
    end
  end
end
