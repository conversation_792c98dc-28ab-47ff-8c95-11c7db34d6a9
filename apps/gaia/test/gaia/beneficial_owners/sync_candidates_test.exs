defmodule Gaia.BeneficialOwners.SyncCandidatesTest do
  use Gaia.DataCase

  alias Gaia.BeneficialOwners
  alias Gaia.BeneficialOwners.Account
  alias Gaia.BeneficialOwners.Holding
  alias Gaia.BeneficialOwners.Report
  alias Gaia.BeneficialOwners.ReportCandidate
  alias Gaia.BeneficialOwners.ReportParticipant
  alias Gaia.Contacts.Contact

  describe "sync_candidates_to_accounts_and_holdings_and_participants/1" do
    setup [:base_setup, :report_setup, :candidates_setup]

    test "creates accounts for layer 2+ candidates", %{
      report: report,
      layer_2_candidate: layer_2_candidate,
      layer_3_candidate: layer_3_candidate
    } do
      # Verify no accounts exist before sync
      assert Repo.all(Account) == []

      # Run the sync
      {:ok, _result} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report.id)

      # Verify accounts were created (only for layer 2 and 3 candidates)
      accounts = Repo.all(Account)
      assert length(accounts) == 2

      # Verify each account has the correct data
      for candidate <- [layer_2_candidate, layer_3_candidate] do
        account = Enum.find(accounts, &(&1.account_name == candidate.account_name))
        assert account != nil
        assert account.company_profile_id == report.company_profile_id
      end

      # Verify contacts were created
      contacts = Repo.all(Contact)
      assert length(contacts) == 2
    end

    test "creates holdings for layer 2 and 3 candidates", %{
      report: report,
      layer_2_candidate: layer_2_candidate,
      layer_3_candidate: layer_3_candidate
    } do
      # Verify no holdings exist before sync
      assert Repo.all(Holding) == []

      # Run the sync
      {:ok, _result} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report.id)

      # Verify holdings were created
      holdings = Repo.all(Holding)
      assert length(holdings) == 2

      # Get the created accounts
      layer_2_account = Repo.get_by(Account, account_name: layer_2_candidate.account_name)
      layer_3_account = Repo.get_by(Account, account_name: layer_3_candidate.account_name)

      # Verify layer 2 holding has correct data
      layer_2_holding = Enum.find(holdings, &(&1.candidate_id == layer_2_candidate.id))
      assert layer_2_holding != nil
      assert layer_2_holding.company_profile_id == report.company_profile_id
      assert layer_2_holding.report_id == report.id
      assert layer_2_holding.beneficial_owner_account_id == layer_2_account.id
      assert layer_2_holding.shares == layer_2_candidate.shares

      # Verify layer 3 holding has correct data
      layer_3_holding = Enum.find(holdings, &(&1.candidate_id == layer_3_candidate.id))
      assert layer_3_holding != nil
      assert layer_3_holding.company_profile_id == report.company_profile_id
      assert layer_3_holding.report_id == report.id
      assert layer_3_holding.beneficial_owner_account_id == layer_3_account.id
      assert layer_3_holding.shares == layer_3_candidate.shares
    end

    test "creates participants", %{
      report: report,
      layer_1_candidate: layer_1_candidate,
      layer_2_candidate: layer_2_candidate,
      layer_3_candidate: layer_3_candidate
    } do
      # Verify no participants exist before sync
      assert Repo.all(ReportParticipant) == []

      # Run the sync
      {:ok, _result} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report.id)

      # Verify participants were created for all layers
      participants = ReportParticipant |> Repo.all() |> Repo.preload(children: :children)
      candidates = ReportCandidate |> Repo.all() |> Repo.preload(children: :children)
      assert length(candidates) == 3
      assert length(participants) == 3

      # Verify each participant has the correct data
      for candidate <- [layer_1_candidate, layer_2_candidate, layer_3_candidate] do
        participant = Enum.find(participants, &(&1.account_name == candidate.account_name))
        assert participant != nil
        assert participant.shareholding_id == candidate.shareholding_id
        assert participant.layer == candidate.layer
        assert participant.candidate_id == candidate.id
      end

      # Make sure all layers of children are added
      for participant <- participants do
        if participant.layer == 1 do
          assert Enum.count(participant.children) == 1
          assert Enum.count(List.first(participant.children).children) == 1
        end
      end
    end

    test "deleting a candidate also deletes its holdings", %{
      report: report,
      layer_2_candidate: layer_2_candidate
    } do
      # Run the sync to create holdings
      {:ok, _result} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report.id)

      # Verify holdings exist
      holdings_before = Repo.all(Holding)
      assert length(holdings_before) == 2

      # Delete the layer 2 candidate
      {:ok, _} = BeneficialOwners.delete_report_candidate(layer_2_candidate)

      # Verify the holding was deleted
      holdings_after = Repo.all(Holding)
      assert holdings_after == []
    end

    test "does not create duplicate holdings when sync is run multiple times", %{
      report: report,
      layer_2_candidate: _layer_2_candidate,
      layer_3_candidate: _layer_3_candidate
    } do
      # Run the sync for the first time
      {:ok, _result1} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report.id)

      # Verify holdings were created
      holdings_after_first_sync = Repo.all(Holding)
      assert length(holdings_after_first_sync) == 2

      # Get the IDs of the holdings after the first sync
      holding_ids_after_first_sync = Enum.map(holdings_after_first_sync, & &1.id)

      # Run the sync again
      {:ok, _result2} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report.id)

      # Verify no new holdings were created
      holdings_after_second_sync = Repo.all(Holding)
      assert length(holdings_after_second_sync) == 2

      # Verify the holdings are the same ones (same IDs)
      holding_ids_after_second_sync = Enum.map(holdings_after_second_sync, & &1.id)
      assert Enum.sort(holding_ids_after_first_sync) == Enum.sort(holding_ids_after_second_sync)

      # Verify the holdings data is unchanged
      for holding <- holdings_after_first_sync do
        same_holding_after_second_sync =
          Enum.find(holdings_after_second_sync, &(&1.id == holding.id))

        assert same_holding_after_second_sync.beneficial_owner_account_id ==
                 holding.beneficial_owner_account_id

        assert same_holding_after_second_sync.shares == holding.shares
        assert same_holding_after_second_sync.candidate_id == holding.candidate_id
      end
    end
  end

  describe "ensure account and depot code are unique" do
    setup [:base_setup, :report_setup, :candidates_with_depot_setup]

    test "ensure account and depot code are unique", %{
      report: report
    } do
      {:ok, _result} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report.id)

      accounts = Repo.all(Account)
      assert length(accounts) == 3
    end
  end

  describe "ensure same candidate only create one account" do
    setup [:base_setup, :report_setup, :second_report_setup, :candidates_with_last_report_setup]

    test "ensure same accounts only create one account", %{
      report: report,
      report_b: report_b
    } do
      {:ok, _result} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report.id)
      {:ok, _result} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report_b.id)

      accounts = Repo.all(Account)
      assert length(accounts) == 2
      assert Enum.map(accounts, & &1.account_name) == ["Child A", "Child A"]
    end

    test "ensure account is deleted if candidate deleted and account not linked to other candidates", %{
      report: report,
      report_b: report_b,
      layer_2_candidate_c: layer_2_candidate_c
    } do
      # Sync both reports to create accounts
      {:ok, _} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report.id)
      {:ok, _} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report_b.id)

      # There should be two accounts with the name "Child A"
      accounts = Repo.all(from a in Account, where: a.account_name == ^layer_2_candidate_c.account_name)
      assert length(accounts) == 2

      # Find the account_id for layer_2_candidate_c
      holding = Repo.get_by(Holding, candidate_id: layer_2_candidate_c.id)
      assert holding
      account_id = holding.beneficial_owner_account_id

      # Delete the candidate (not referenced by any other candidate)
      {:ok, _} = BeneficialOwners.delete_report_candidate(layer_2_candidate_c)

      # Sync again
      {:ok, _} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report.id)
      {:ok, _} = BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report_b.id)

      # The account for layer_2_candidate_c should be deleted
      refute Repo.get(Account, account_id)

      # There should still be one account with that name left
      accounts_after = Repo.all(from a in Account, where: a.account_name == ^layer_2_candidate_c.account_name)
      assert length(accounts_after) == 1
    end
  end

  defp base_setup(_) do
    [company: company_profile()]
  end

  defp report_setup(%{company: company}) do
    report =
      Repo.insert!(%Report{
        report_date: Faker.Date.backward(30),
        company_profile_id: company.id,
        is_user_uploaded: false
      })

    [report: report]
  end

  defp second_report_setup(%{company: company}) do
    report_b =
      Repo.insert!(%Report{
        report_date: Faker.Date.backward(30),
        company_profile_id: company.id,
        is_user_uploaded: false
      })

    [report_b: report_b]
  end

  defp candidates_setup(%{report: report}) do
    # Create layer 1 candidate
    {:ok, layer_1_candidate} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        layer: 1,
        shares: 10_000,
        account_name: Faker.Company.name(),
        address_line_one: Faker.Address.street_address(),
        address_city: Faker.Address.city(),
        address_state: Faker.Address.state(),
        address_postcode: Faker.Address.postcode(),
        address_country: Faker.Address.country(),
        status: :done
      })

    # Create layer 2 candidate
    {:ok, layer_2_candidate} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        layer: 2,
        shares: 5000,
        account_name: Faker.Company.name(),
        address_line_one: Faker.Address.street_address(),
        address_city: Faker.Address.city(),
        address_state: Faker.Address.state(),
        address_postcode: Faker.Address.postcode(),
        address_country: Faker.Address.country(),
        status: :done,
        parent_id: layer_1_candidate.id
      })

    # Create layer 3 candidate
    {:ok, layer_3_candidate} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        layer: 3,
        shares: 2500,
        account_name: Faker.Company.name(),
        address_line_one: Faker.Address.street_address(),
        address_city: Faker.Address.city(),
        address_state: Faker.Address.state(),
        address_postcode: Faker.Address.postcode(),
        address_country: Faker.Address.country(),
        status: :done,
        parent_id: layer_2_candidate.id
      })

    [
      layer_1_candidate: layer_1_candidate,
      layer_2_candidate: layer_2_candidate,
      layer_3_candidate: layer_3_candidate,
      candidates: [layer_1_candidate, layer_2_candidate, layer_3_candidate]
    ]
  end

  defp candidates_with_last_report_setup(%{report: report, report_b: report_b}) do
    # Create layer 1 candidate
    {:ok, layer_1_candidate} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        account_name: "Parent Company 1",
        shares: 10_000,
        layer: 1
      })

    {:ok, layer_2_candidate} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        account_name: "Child A",
        shares: 10_000,
        layer: 2,
        parent_id: layer_1_candidate.id,
        status: :done
      })

    {:ok, layer_1_candidate_b} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report_b.id,
        account_name: "Parent Company 1",
        shares: 10_000,
        layer: 1
      })

    {:ok, layer_2_candidate_b} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report_b.id,
        account_name: "Child A",
        shares: 10_000,
        layer: 2,
        parent_id: layer_1_candidate.id,
        last_report_candidate_id: layer_2_candidate.id,
        status: :done
      })

    {:ok, layer_1_candidate_c} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        account_name: "Parent Company 2",
        shares: 10_000,
        layer: 1
      })

    {:ok, layer_2_candidate_c} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        account_name: "Child A",
        shares: 10_000,
        layer: 2,
        parent_id: layer_1_candidate_c.id,
        status: :done
      })

    [
      layer_1_candidate: layer_1_candidate,
      layer_2_candidate: layer_2_candidate,
      layer_1_candidate_b: layer_1_candidate_b,
      layer_2_candidate_b: layer_2_candidate_b,
      layer_1_candidate_c: layer_1_candidate_c,
      layer_2_candidate_c: layer_2_candidate_c
    ]
  end

  defp candidates_with_depot_setup(%{report: report}) do
    # Create a parent candidate
    {:ok, layer_1_candidate} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        account_name: "Parent Company 1",
        shares: 30_000,
        layer: 1
      })

    {:ok, layer_2_candidate1} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        account_name: "Child Company A",
        shares: 10_000,
        layer: 2,
        parent_id: layer_1_candidate.id,
        depot: "Depot 1",
        status: :done
      })

    {:ok, layer_2_candidate2} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        account_name: "Child Company A",
        shares: 10_000,
        layer: 2,
        parent_id: layer_1_candidate.id,
        depot: "Depot 2",
        status: :done
      })

    {:ok, layer_2_candidate3} =
      BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        account_name: "Child Company B",
        shares: 10_000,
        layer: 2,
        parent_id: layer_1_candidate.id,
        depot: "Depot 2",
        status: :done
      })

    [
      layer_1_parent_candidate: layer_1_candidate,
      layer_2_candidate1: layer_2_candidate1,
      layer_2_candidate2: layer_2_candidate2,
      layer_2_candidate3: layer_2_candidate3
    ]
  end
end
