defmodule Gaia.ContactsTest do
  use Gaia.DataCase

  alias Gaia.Contacts
  alias Gaia.Repo

  describe "create_contact/1" do
    test "a contact cannot be created if the email already exists as a contact for that company" do
      company_profile = company_profile()
      contact1 = contact(company_profile)

      contact_input = %{
        company_profile_id: company_profile.id,
        lead_identified_at: NaiveDateTime.utc_now(:second),
        contact_source: :subscribe_form,
        email: contact1.email
      }

      assert {:error,
              %Ecto.Changeset{
                errors: [
                  company_profile_id:
                    {"has already been taken",
                     [
                       constraint: :unique,
                       constraint_name: "contacts_contacts_company_profile_id_email_index"
                     ]}
                ]
              }} = Contacts.create_contact(contact_input)
    end

    test "ensure email can be null when creating a new contact" do
      %_{id: company_profile_id} = company_profile()

      contact_input = %{
        company_profile_id: company_profile_id,
        lead_identified_at: NaiveDateTime.utc_now(:second),
        contact_source: :registry_import,
        email: nil
      }

      assert {:ok,
              %Contacts.Contact{
                company_profile_id: ^company_profile_id,
                lead_identified_at: _,
                contact_source: :registry_import,
                email: nil
              }} = Contacts.create_contact(contact_input)
    end
  end

  describe "update_contact/1" do
    test "a contact can be updated with a nil email" do
      company_profile = company_profile()
      %{id: contact_id} = contact = contact(company_profile)

      contact_input = %{
        company_profile_id: company_profile.id,
        lead_identified_at: NaiveDateTime.utc_now(:second),
        email: nil
      }

      assert {:ok, %Contacts.Contact{id: ^contact_id, email: nil}} = Contacts.update_contact(contact, contact_input)
    end

    test "ensure null email can be updated properly" do
      %_{id: company_profile_id} = company_profile()

      contact_input = %{
        company_profile_id: company_profile_id,
        lead_identified_at: NaiveDateTime.utc_now(:second),
        contact_source: :registry_import,
        email: nil
      }

      assert {:ok, contact} = Contacts.create_contact(contact_input)

      assert {:ok, %Contacts.Contact{email: "<EMAIL>"}} =
               Contacts.update_contact(contact, %{email: "<EMAIL>"})
    end
  end

  describe "contacts_query/2" do
    # Filters to test:
    # %{
    #   filters: [
    #     %{key: "search_with_investor_users", value: ""},
    #     %{key: "tags", value: ""},
    #     %{key: "has_investor_hub_user", value: "none"},
    #     %{key: "lead_status", value: "none"},
    #     %{key: "subscription_scope", value: "general"},
    #     %{key: "shareholder_status", value: "none"},
    #     %{key: "subscription_status", value: "global-unsubscribed"},
    #     %{key: "newholder_status", value: "none"},
    #     %{key: "hnw_status", value: "none"},
    #     %{key: "location", value: ""},
    #     %{key: "hub_sign_ups_days_ago", value: "none"}
    #   ],
    #   orders: [%{key: "inserted_at", value: "desc"}]
    # }

    test "returns all contacts by default" do
      company_profile = company_profile()
      contact1 = contact(company_profile)
      contact2 = contact(company_profile)
      contact3 = contact(company_profile)

      # Create contact for another company
      # Should not be returned
      company_profile_2 = company_profile()
      contact(company_profile_2)

      results =
        company_profile.id
        |> Contacts.contacts_query()
        |> Repo.all()

      assert length(results) == 3

      assert in_list(results, contact1)
      assert in_list(results, contact2)
      assert in_list(results, contact3)
    end

    test "subscription_scope = general ignores unsubs" do
      company_profile = company_profile()
      contact1 = contact(company_profile)
      contact2 = contact(company_profile)
      unsubbed_contact = contact(company_profile)
      contact_unsubscribe(unsubbed_contact, %{scope: :general})

      options = %{filters: [%{key: "subscription_scope", value: "general"}]}

      results =
        company_profile.id
        |> Contacts.contacts_query(options)
        |> Repo.all()

      assert in_list(results, contact1)
      assert in_list(results, contact2)
      refute in_list(results, unsubbed_contact)
    end

    test "filters contacts by global-unsubscribed status" do
      company_profile = company_profile()
      contact = contact(company_profile)
      unsubbed_contact = contact(company_profile)
      contact_global_unsubscribe(unsubbed_contact)

      results =
        company_profile.id
        |> Contacts.contacts_query(%{
          filters: [
            %{key: "subscription_status", value: "global-unsubscribed"}
          ]
        })
        |> Repo.all()

      assert length(results) == 1

      assert in_list(results, unsubbed_contact)
      refute in_list(results, contact)
    end

    test "filters contacts by contact_source" do
      company_profile = company_profile()
      contact_bulk_import = contact(company_profile, %{contact_source: :bulk_import})
      contact_hub_signup = contact(company_profile, %{contact_source: :hub_signup})
      contact_manual_creation = contact(company_profile, %{contact_source: :manual_creation})
      contact_registry_import = contact(company_profile, %{contact_source: :registry_import})
      contact_subscribe_form = contact(company_profile, %{contact_source: :subscribe_form})

      get_contacts = fn company_profile_id, sources ->
        company_profile_id
        |> Contacts.contacts_query(%{filters: [%{key: "sources", value: sources}], orders: [%{key: "id", value: "asc"}]})
        |> Repo.all()
        |> Gaia.Repo.preload([:comms_unsubscribes, :company_profile, :global_unsubscribe, :suppression, :tags])
      end

      results_sources_bulk_import = get_contacts.(company_profile.id, "bulk_import")
      results_sources_hub_signup = get_contacts.(company_profile.id, "hub_signup")
      results_sources_manual_creation = get_contacts.(company_profile.id, "manual_creation")
      results_sources_registry_import = get_contacts.(company_profile.id, "registry_import")
      results_sources_subscribe_form = get_contacts.(company_profile.id, "subscribe_form")

      assert length(results_sources_bulk_import) == 1
      assert length(results_sources_hub_signup) == 1
      assert length(results_sources_manual_creation) == 1
      assert length(results_sources_registry_import) == 1
      assert length(results_sources_subscribe_form) == 1

      assert [contact_bulk_import] == results_sources_bulk_import
      assert [contact_hub_signup] == results_sources_hub_signup
      assert [contact_manual_creation] == results_sources_manual_creation
      assert [contact_registry_import] == results_sources_registry_import
      assert [contact_subscribe_form] == results_sources_subscribe_form

      assert [contact_bulk_import, contact_manual_creation, contact_registry_import] ==
               get_contacts.(company_profile.id, "bulk_import,manual_creation,registry_import")
    end

    test "filters contacts by beneficial owner account presence" do
      company_profile = company_profile()

      contact_with_bo = contact(company_profile)
      contact_without_bo = contact(company_profile)

      beneficial_owner_account(contact_with_bo)

      # Test has_beneficial_owner_account = true
      results_with_bo =
        company_profile.id
        |> Contacts.contacts_query(%{filters: [%{key: "has_beneficial_owner_account", value: "true"}]})
        |> Repo.all()

      assert length(results_with_bo) == 1
      assert in_list(results_with_bo, contact_with_bo)
      refute in_list(results_with_bo, contact_without_bo)

      # Test has_beneficial_owner_account = false
      results_without_bo =
        company_profile.id
        |> Contacts.contacts_query(%{filters: [%{key: "has_beneficial_owner_account", value: "false"}]})
        |> Repo.all()

      assert length(results_without_bo) == 1
      assert in_list(results_without_bo, contact_without_bo)
      refute in_list(results_without_bo, contact_with_bo)
    end

    test "filters contacts by beneficial owner shares count" do
      company_profile = company_profile()

      contact_high_shares = contact(company_profile)
      contact_low_shares = contact(company_profile)
      contact_no_shares = contact(company_profile)

      beneficial_owner_account_with_shares(contact_high_shares, 1000)
      beneficial_owner_account_with_shares(contact_low_shares, 100)

      # Test minimum shares filter
      results_min_500 =
        company_profile.id
        |> Contacts.contacts_query(%{filters: [%{key: "beneficial_owner_shares_count", value: "500"}]})
        |> Repo.all()

      assert length(results_min_500) == 1
      assert in_list(results_min_500, contact_high_shares)
      refute in_list(results_min_500, contact_low_shares)
      refute in_list(results_min_500, contact_no_shares)

      # Test range filter
      results_range =
        company_profile.id
        |> Contacts.contacts_query(%{filters: [%{key: "beneficial_owner_shares_count", value: "50,200"}]})
        |> Repo.all()

      assert length(results_range) == 1
      assert in_list(results_range, contact_low_shares)
      refute in_list(results_range, contact_high_shares)
      refute in_list(results_range, contact_no_shares)
    end
  end

  test "campaign_email_audience_list" do
    company_profile = company_profile()

    # Create 1 manual contact (not hub, not imported, not current shareholder)
    manual_contact = contact(company_profile)

    # Create 1 imported contact
    imported_contact = contact(company_profile, %{imported_at: NaiveDateTime.local_now()})

    # Create 1 hub contact
    %{contact: hub_contact} = investor_user(company_profile)

    # Create 1 old shareholder
    %{contact: old_shareholder_contact} =
      shareholding(%{company_profile_id: company_profile.id}, [
        %{
          closing_balance: 1_000,
          movement: 1_000,
          opening_balance: 0,
          settled_at: Date.add(today(), -366)
        }
      ])

    # Create 1 new shareholder
    %{contact: new_shareholder_contact} = shareholding(:past_30_days_new, %{company_profile_id: company_profile.id})

    # Create 1 unsubbed contact
    unsubbed_contact = contact(company_profile)
    contact_unsubscribe(unsubbed_contact, %{scope: :general})

    # Create 1 suppressed contact
    suppressed_contact = contact(company_profile)
    contact_suppression(suppressed_contact)

    # Create 1 global unsubbed contact
    global_unsubbed_contact = contact(company_profile)
    contact_global_unsubscribe(global_unsubbed_contact)

    results = get_results(company_profile, "general,HUB")
    refute in_list(results, imported_contact), "imported_contact shouldn't be in results"
    assert in_list(results, hub_contact), "hub_contact not in results"
    refute in_list(results, new_shareholder_contact), "new_shareholder_contact shouldn't be in results"
    refute in_list(results, old_shareholder_contact), "old_shareholder_contact shouldn't be in results"
    refute in_list(results, manual_contact), "manual_contact shouldn't be in results"
    refute in_list(results, unsubbed_contact), "unsubbed_contact shouldn't be in results"
    refute in_list(results, suppressed_contact), "suppressed_contact shouldn't be in results"
    refute in_list(results, global_unsubbed_contact), "global_unsubbed_contact shouldn't be in results"

    results = get_results(company_profile, "general,IMPORTED")
    assert in_list(results, imported_contact), "imported_contact not in results"
    refute in_list(results, hub_contact), "hub_contact shouldn't be in results"
    refute in_list(results, new_shareholder_contact), "new_shareholder_contact shouldn't be in results"
    refute in_list(results, old_shareholder_contact), "old_shareholder_contact shouldn't be in results"
    refute in_list(results, manual_contact), "manual_contact shouldn't be in results"
    refute in_list(results, unsubbed_contact), "unsubbed_contact shouldn't be in results"
    refute in_list(results, unsubbed_contact), "unsubbed_contact shouldn't be in results"
    refute in_list(results, suppressed_contact), "suppressed_contact shouldn't be in results"
    refute in_list(results, global_unsubbed_contact), "global_unsubbed_contact shouldn't be in results"

    results = get_results(company_profile, "general,NEW_SHAREHOLDERS")
    refute in_list(results, imported_contact), "imported_contact shouldn't be in results"
    refute in_list(results, hub_contact), "hub_contact shouldn't be in results"
    assert in_list(results, new_shareholder_contact), "new_shareholder_contact not in results"
    refute in_list(results, old_shareholder_contact), "old_shareholder_contact shouldn't be in results"
    refute in_list(results, manual_contact), "manual_contact shouldn't be in results"
    refute in_list(results, unsubbed_contact), "unsubbed_contact shouldn't be in results"
    refute in_list(results, unsubbed_contact), "unsubbed_contact shouldn't be in results"
    refute in_list(results, suppressed_contact), "suppressed_contact shouldn't be in results"
    refute in_list(results, global_unsubbed_contact), "global_unsubbed_contact shouldn't be in results"

    results = get_results(company_profile, "general,ALL_SHAREHOLDERS")
    refute in_list(results, imported_contact), "imported_contact shouldn't be in results"
    refute in_list(results, hub_contact), "hub_contact shouldn't be in results"
    assert in_list(results, new_shareholder_contact), "new_shareholder_contact not in results"
    assert in_list(results, old_shareholder_contact), "old_shareholder_contact not in results"
    refute in_list(results, manual_contact), "manual_contact shouldn't be in results"
    refute in_list(results, unsubbed_contact), "unsubbed_contact shouldn't be in results"
    refute in_list(results, unsubbed_contact), "unsubbed_contact shouldn't be in results"
    refute in_list(results, suppressed_contact), "suppressed_contact shouldn't be in results"
    refute in_list(results, global_unsubbed_contact), "global_unsubbed_contact shouldn't be in results"

    results = get_results(company_profile, "general,ALL")
    assert in_list(results, imported_contact), "imported_contact not in results"
    assert in_list(results, hub_contact), "hub_contact not in results"
    assert in_list(results, new_shareholder_contact), "new_shareholder_contact not in results"
    assert in_list(results, old_shareholder_contact), "old_shareholder_contact not in results"
    assert in_list(results, manual_contact), "manual_contact not in results"
    refute in_list(results, unsubbed_contact), "unsubbed_contact shouldn't be in results"
    refute in_list(results, unsubbed_contact), "unsubbed_contact shouldn't be in results"
    refute in_list(results, suppressed_contact), "suppressed_contact shouldn't be in results"
    refute in_list(results, global_unsubbed_contact), "global_unsubbed_contact shouldn't be in results"
  end

  describe "create_static_list_with_contacts/2" do
    setup do
      company_profile = company_profile()

      contact1 = contact(company_profile)
      contact2 = contact(company_profile)

      [company_profile: company_profile, contact1: contact1, contact2: contact2]
    end

    test "creates a static list without contacts", %{
      company_profile: company_profile
    } do
      static_list_input = %{
        name: "Test Static List",
        company_profile_id: company_profile.id,
        background_color: "#FFFFFF",
        text_color: "#000000"
      }

      {:ok, static_list} = Contacts.create_static_list_with_contacts(static_list_input, [])
      assert Contacts.get_static_list_member_count(static_list.id) == 0
    end

    test "creates a static list with contacts", %{
      company_profile: company_profile,
      contact1: contact1,
      contact2: contact2
    } do
      static_list_input = %{
        name: "Test Static List",
        company_profile_id: company_profile.id,
        background_color: "#FFFFFF",
        text_color: "#000000"
      }

      contact_ids = [contact1.id, contact2.id]

      assert {:ok, static_list} = Contacts.create_static_list_with_contacts(static_list_input, contact_ids)

      assert Contacts.get_static_list_member_count(static_list.id) == 2
    end

    test "does not create a static list if the name already exists", %{company_profile: company_profile} do
      {:ok, static_list_1} =
        Contacts.create_static_list_with_contacts(
          %{
            name: "Test Static List",
            company_profile_id: company_profile.id,
            background_color: "#FFFFFF",
            text_color: "#000000"
          },
          []
        )

      static_list_2_input = %{
        name: static_list_1.name,
        company_profile_id: company_profile.id,
        background_color: "#FFFFFF",
        text_color: "#000000"
      }

      assert {:error, %Ecto.Changeset{}} = Contacts.create_static_list_with_contacts(static_list_2_input, [])
    end
  end

  describe "delete_static_list_member/1" do
    setup do
      company_profile = company_profile()

      contact = contact(company_profile)

      static_list =
        static_list(%{
          name: "Test Static List",
          company_profile_id: company_profile.id,
          background_color: "#FFFFFF",
          text_color: "#000000"
        })

      static_list_member =
        static_list_member(%{
          contact_id: contact.id,
          static_list_id: static_list.id
        })

      [
        company_profile: company_profile,
        contact: contact,
        static_list: static_list,
        static_list_member: static_list_member
      ]
    end

    test "ensure removes a contact from a static list", %{
      static_list: static_list,
      static_list_member: static_list_member
    } do
      Contacts.delete_static_list_member(static_list_member)

      assert Contacts.get_static_list_member_count(static_list.id) == 0
    end
  end

  describe "existing_static_lists_by_company_profile/2" do
    setup do
      company_profile = company_profile()

      {:ok, static_list} =
        Contacts.create_static_list(%{
          name: "Test Static List",
          company_profile_id: company_profile.id,
          background_color: "#FFFFFF",
          text_color: "#000000"
        })

      [company_profile: company_profile, static_list: static_list]
    end

    test "returns the existing static lists for a company profile", %{
      company_profile: company_profile,
      static_list: static_list
    } do
      static_lists = Contacts.existing_static_lists_by_company_profile(company_profile)
      assert length(static_lists) == 1

      assert [^static_list] = static_lists
    end

    test "returns the existing static lists for a company profile regardless of other lists for other companies", %{
      company_profile: company_profile,
      static_list: static_list
    } do
      another_profile = company_profile()

      Contacts.create_static_list(%{
        name: "new list",
        company_profile_id: another_profile.id,
        background_color: "#777",
        text_color: "#888"
      })

      static_lists = Contacts.existing_static_lists_by_company_profile(company_profile)
      assert length(static_lists) == 1

      assert [^static_list] = static_lists
    end

    test "returns only non invalidated lists", %{
      company_profile: company_profile,
      static_list: static_list
    } do
      {:ok, invalidated_list} =
        Contacts.create_static_list(%{
          name: "new list",
          company_profile_id: company_profile.id,
          background_color: "#777",
          text_color: "#888"
        })

      Contacts.invalidate_static_list(invalidated_list, %{})

      static_lists = Contacts.existing_static_lists_by_company_profile(company_profile)
      assert length(static_lists) == 1

      assert [^static_list] = static_lists
    end

    test "returns lists in alphabetical order by name" do
      company_profile = company_profile()

      {:ok, list_c} =
        Contacts.create_static_list(%{
          name: "C list",
          company_profile_id: company_profile.id,
          background_color: "#777",
          text_color: "#888"
        })

      {:ok, list_a} =
        Contacts.create_static_list(%{
          name: "A list",
          company_profile_id: company_profile.id,
          background_color: "#999",
          text_color: "#000"
        })

      {:ok, list_b} =
        Contacts.create_static_list(%{
          name: "B list",
          company_profile_id: company_profile.id,
          background_color: "#555",
          text_color: "#fff"
        })

      static_lists = Contacts.existing_static_lists_by_company_profile(company_profile)
      assert length(static_lists) == 3

      assert [^list_a, ^list_b, ^list_c] = static_lists
    end
  end

  describe "insert_recommended_dynamic_lists/1" do
    test "ensure the recommended dynamic lists for premium company are inserted " do
      company_profile = company_profile()
      Contacts.insert_recommended_dynamic_lists(company_profile)

      dynamic_lists = Repo.all(Contacts.DynamicList)
      assert length(dynamic_lists) == 6

      assert Enum.all?(dynamic_lists, fn list ->
               list.name in [
                 "All shareholders",
                 "All hub contacts",
                 "Upgraded in the last 30 days",
                 "Downgraded in the last 30 days",
                 "Churned in the last 30 days",
                 "Returned in the last 30 days"
               ]
             end)
    end

    test "ensure the recommended dynamic lists for base company are inserted" do
      company_profile = company_profile(%{is_premium: false})

      Contacts.insert_recommended_dynamic_lists(company_profile)

      dynamic_lists = Repo.all(Contacts.DynamicList)
      assert length(dynamic_lists) == 2

      assert Enum.all?(dynamic_lists, fn list ->
               list.name in [
                 "All nominated shareholders",
                 "All hub contacts"
               ]
             end)
    end

    test "ensure the recommended dynamic are not duplicated when moving company from base to premium" do
      company_profile = company_profile(%{is_premium: false})

      Contacts.insert_recommended_dynamic_lists(company_profile)

      dynamic_lists = Repo.all(Contacts.DynamicList)
      assert length(dynamic_lists) == 2

      assert Enum.all?(dynamic_lists, fn list ->
               list.name in [
                 "All nominated shareholders",
                 "All hub contacts"
               ]
             end)

      {:ok, updated_profile} = Gaia.Companies.update_profile(company_profile, %{is_premium: true})
      Contacts.insert_recommended_dynamic_lists(updated_profile)
      updated_dynamic_lists = Repo.all(Contacts.DynamicList)
      assert length(updated_dynamic_lists) == 7

      assert Enum.all?(dynamic_lists, fn list ->
               list.name in [
                 "All nominated shareholders",
                 # Both Premium and Base companies have this item
                 "All hub contacts",
                 "All shareholders",
                 "Upgraded in the last 30 days",
                 "Downgraded in the last 30 days",
                 "Churned in the last 30 days",
                 "Returned in the last 30 days"
               ]
             end)
    end
  end

  describe "upsert_scores/2" do
    setup do
      company_profile = company_profile()

      contact1 =
        contact(company_profile, %{
          email: "<EMAIL>",
          email_engagement_score: 1.1
        })

      contact2 = contact(company_profile, %{email: "<EMAIL>"})
      contact3 = contact(company_profile, %{email: "<EMAIL>"})

      {:ok, company_profile: company_profile, contact1: contact1, contact2: contact2, contact3: contact3}
    end

    test "ensure updates email engagement scores for contacts", %{
      company_profile: company_profile,
      contact1: contact1,
      contact2: contact2,
      contact3: contact3
    } do
      scores_to_upsert = [
        %{contact_id: contact1.id, total_score: 10.5},
        %{contact_id: contact2.id, total_score: 5.2},
        %{contact_id: 99_999, total_score: 1.0}
      ]

      assert :ok = Contacts.upsert_scores(scores_to_upsert, company_profile.id)

      updated_contact1 = Contacts.get_contact!(contact1.id)
      updated_contact2 = Contacts.get_contact!(contact2.id)
      updated_contact3 = Contacts.get_contact!(contact3.id)

      assert updated_contact1.email_engagement_score == 10.5

      assert updated_contact2.email_engagement_score == 5.2

      assert updated_contact3.email_engagement_score == nil
      assert updated_contact3.updated_at == contact3.updated_at

      assert Contacts.get_contact(99_999) == nil
    end
  end

  defp get_results(company_profile, options) do
    company_profile.id
    |> Contacts.contacts_query(%{
      filters: [
        %{key: "campaign_email_audience_list", value: options}
      ]
    })
    |> Repo.all()
  end

  defp in_list(list, thing) do
    Enum.any?(list, fn item -> item.id == thing.id end)
  end

  defp beneficial_owner_account(contact) do
    Repo.insert!(%Gaia.BeneficialOwners.Account{
      contact_id: contact.id,
      company_profile_id: contact.company_profile_id,
      account_name: "Test Account #{contact.id}"
    })
  end

  defp beneficial_owner_account_with_shares(contact, shares) do
    account = beneficial_owner_account(contact)

    report =
      Repo.insert!(%Gaia.BeneficialOwners.Report{
        company_profile_id: contact.company_profile_id,
        report_date: Date.utc_today(),
        type: :completed
      })

    {:ok, candidate} =
      Gaia.BeneficialOwners.create_report_candidate(%{
        report_id: report.id,
        layer: 2,
        shares: shares,
        account_name: account.account_name,
        address_line_one: "123 Test St",
        address_city: "Test City",
        address_state: "Test State",
        address_postcode: "12345",
        address_country: "Test Country",
        status: :done
      })

    Repo.insert!(%Gaia.BeneficialOwners.Holding{
      beneficial_owner_account_id: account.id,
      report_id: report.id,
      company_profile_id: contact.company_profile_id,
      candidate_id: candidate.id,
      shares: shares
    })

    account
  end
end
