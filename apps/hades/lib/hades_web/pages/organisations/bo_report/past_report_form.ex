defmodule HadesWeb.Pages.Organisations.BoReport.PastReportForm do
  @moduledoc """
  Reusable component for document uploads
  """
  use HadesWeb, :live_component

  @impl true
  def mount(socket) do
    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign(:past_report_date, Date.utc_today())
      |> assign(:processing, false)
      |> allow_upload(:document, accept: ~w(.csv .xlsx .xls), max_entries: 1, max_file_size: 10_000_000)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <form phx-submit="save_document" phx-change="validate" phx-target={@myself} class="space-y-4">
        <div class="flex gap-4 p-2 border-b border-gray-200 dark:border-gray-700 pb-4">
          <div class="flex flex-1 flex-col h-full space-y-4">
            <div
              class="mt-1 flex flex-1 justify-center px-6 pt-5 pb-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-md h-56"
              phx-drop-target={@uploads.document.ref}
            >
              <div class="space-y-2 text-center flex flex-col justify-center">
                <PC.icon name="hero-document" class="mx-auto h-12 w-12 text-gray-400" />
                <div class="flex text-sm text-gray-600 dark:text-gray-400 justify-center">
                  <label
                    for={@uploads.document.ref}
                    class="relative cursor-pointer rounded-md font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                  >
                    <span>Upload file</span>
                    <.live_file_input upload={@uploads.document} class="sr-only" />
                  </label>
                  <p class="pl-1">or drag and drop</p>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {"CSV, XLSX or XLS up to 10MB"}
                </p>
              </div>
            </div>
            <%= if length(@uploads.document.entries) > 0 do %>
              <div class="mt-2">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Selected Files
                </h4>
                <ul class="space-y-2">
                  <%= for entry <- @uploads.document.entries do %>
                    <li class="flex items-center justify-between text-sm bg-gray-50 dark:bg-gray-800 p-2 rounded">
                      <div class="flex items-center">
                        <PC.icon name="hero-document" class="w-4 h-4 text-gray-500 mr-2" />
                        <span class="text-gray-700 dark:text-gray-300 truncate max-w-xs">
                          {entry.client_name}
                        </span>
                      </div>
                      <button
                        type="button"
                        phx-click="cancel_upload"
                        phx-value-ref={entry.ref}
                        phx-target={@myself}
                        class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                      >
                        <PC.icon name="hero-x-mark" class="w-4 h-4" />
                      </button>
                    </li>
                    <%= for err <- upload_errors(@uploads.document, entry) do %>
                      <div class="text-red-500 text-xs mt-1 pl-2">{error_to_string(err)}</div>
                    <% end %>
                  <% end %>
                </ul>
              </div>
            <% end %>
          </div>

          <div class="flex flex-1 flex-col gap-2">
            <div>
              <label
                for="past_report_date"
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                Past report date
              </label>
              <input
                type="date"
                id="past_report_date"
                name="past_report_date"
                value={@past_report_date}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-gray-300"
              />
            </div>
            <div>
              <label
                for="custom_prompt"
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                Custom prompt
              </label>
              <textarea
                id="custom_prompt"
                name="custom_prompt"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-gray-300"
              ></textarea>
            </div>
          </div>
        </div>

        <div class="mt-4 flex justify-end">
          <button
            type="submit"
            class="inline-flex items-center px-3 py-1.5 text-sm font-medium rounded text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            disabled={length(@uploads.document.entries) == 0 || @processing}
          >
            <PC.icon name="hero-arrow-up-tray" class="w-4 h-4 mr-1.5" />
            <span>Upload</span>
          </button>
        </div>
      </form>
    </div>
    """
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel_upload", %{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :document, ref)}
  end

  @impl true
  def handle_event("save_document", params, socket) do
    socket =
      socket
      |> assign(:past_report_date, Date.from_iso8601!(params["past_report_date"]))
      |> assign(:processing, true)

    report_id = socket.assigns.report_id
    custom_prompt = params["custom_prompt"]

    consume_uploaded_entries(socket, :document, fn %{path: path}, entry ->
      file_type = determine_file_type(entry.client_name)

      upload = %Plug.Upload{
        path: path,
        filename: entry.client_name,
        content_type: entry.client_type
      }

      case Gaia.BeneficialOwners.CandidateImporterAi.extract_upload_data(upload, "", file_type) do
        {:ok, file_content} ->
          id = self()
          send(id, {:processing_document, :started})

          Task.start(fn ->
            result =
              Gaia.BeneficialOwners.CandidateImporterAi.extract_and_import_past_report(
                file_content,
                report_id,
                socket.assigns.past_report_date,
                custom_prompt,
                socket.assigns.is_uk,
                file_type
              )

            handle_import_result(result, id)
          end)

        {:error, error} ->
          send(self(), {:flash, :error, "Failed to process document: #{error}"})
          send(self(), {:processing_document, :finished})
      end
    end)

    send(self(), {:close_upload_past_placement_report_modal})
    {:noreply, assign(socket, :processing, false)}
  end

  defp error_to_string(:too_large), do: "File is too large (max 10MB)"
  defp error_to_string(:not_accepted), do: "Invalid file type (must be CSV, XLSX, or XLS)"

  defp determine_file_type(filename) do
    case Path.extname(filename) do
      ".csv" ->
        :csv

      ".xlsx" ->
        :xlsx

      ".xls" ->
        :xls

      _ ->
        nil
    end
  end

  defp handle_import_result(result, id) do
    case result do
      {:ok, %{candidates_count: count, has_errors: true}} ->
        send(
          id,
          {:flash, :error,
           "Document processed successfully. #{count} candidates imported. However, some candidates were not imported due to errors. CLICK the refresh button to see the candidates."}
        )

      {:ok, %{candidates_count: count, has_errors: false}} ->
        send(
          id,
          {:flash, :info,
           "Document processed successfully. #{count} candidates imported. CLICK the refresh button to see the candidates."}
        )

      {:error, error} ->
        send(id, {:flash, :error, "Failed to process document: #{error}"})
    end

    send(id, {:processing_document, :finished})
  end
end
