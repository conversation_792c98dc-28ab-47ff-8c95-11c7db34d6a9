defmodule HadesWeb.Pages.Organisations.BoReport.ReportCandidate.NomineeForm do
  @moduledoc """
  Form for uploading nominee documents
  """
  use HadesWeb, :live_component

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign(:form, %{
        uploaded_files: []
      })
      |> assign(:processing, false)
      |> allow_upload(:document,
        accept: ~w(.pdf .csv .xlsx .xls),
        max_entries: 1,
        # 10MB
        max_file_size: 10_000_000
      )

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <tr class="bg-blue-50/50 dark:bg-blue-900/20">
      <td colspan="9" class="px-6 py-3">
        <div class="flex flex-col space-y-2">
          <div class="flex items-center justify-between mb-1">
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Upload Documents</h3>
            <div class="flex space-x-2">
              <button
                type="button"
                phx-click="close_form"
                phx-target={@parent_id}
                class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <PC.icon name="hero-x-mark" class="w-4 h-4" />
              </button>
            </div>
          </div>

          <form
            phx-submit="save_document"
            phx-change="validate"
            phx-target={@myself}
            class="space-y-4"
          >
            <div class="grid grid-cols-3 gap-6">
              <div class="col-span-2">
                <div class="flex flex-col h-full space-y-4">
                  <div class="flex flex-row items-center gap-2">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Use this example to upload the candidates. Do not change the column numbers or file name.
                    </h4>
                    <a
                      target="_blank"
                      class="text-sm font-medium text-gray-500 dark:text-gray-400"
                      href="https://storage.googleapis.com/leaf-prod/uploads/beneficial_owners/upload_nominee_example/custom_form.csv"
                    >
                      See example
                    </a>
                  </div>
                  <div>
                    <div
                      class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-md h-48"
                      phx-drop-target={@uploads.document.ref}
                    >
                      <div class="space-y-2 text-center flex flex-col justify-center">
                        <PC.icon name="hero-document" class="mx-auto h-12 w-12 text-gray-400" />
                        <div class="flex text-sm text-gray-600 dark:text-gray-400 justify-center">
                          <label
                            for={@uploads.document.ref}
                            class="relative cursor-pointer rounded-md font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                          >
                            <span>Upload file</span>
                            <.live_file_input upload={@uploads.document} class="sr-only" />
                          </label>
                          <p class="pl-1">or drag and drop</p>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                          PDF, CSV, XLSX or XLS up to 10MB
                        </p>
                      </div>
                    </div>
                  </div>

                  <%= if length(@uploads.document.entries) > 0 do %>
                    <div class="mt-2">
                      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Selected Files
                      </h4>
                      <ul class="space-y-2">
                        <%= for entry <- @uploads.document.entries do %>
                          <li class="flex items-center justify-between text-sm bg-gray-50 dark:bg-gray-800 p-2 rounded">
                            <div class="flex items-center">
                              <PC.icon name="hero-document" class="w-4 h-4 text-gray-500 mr-2" />
                              <span class="text-gray-700 dark:text-gray-300 truncate max-w-xs">
                                {entry.client_name}
                              </span>
                            </div>
                            <button
                              type="button"
                              phx-click="cancel_upload"
                              phx-value-ref={entry.ref}
                              phx-target={@myself}
                              class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                            >
                              <PC.icon name="hero-x-mark" class="w-4 h-4" />
                            </button>
                          </li>
                          <%= for err <- upload_errors(@uploads.document, entry) do %>
                            <div class="text-red-500 text-xs mt-1 pl-2">{error_to_string(err)}</div>
                          <% end %>
                        <% end %>
                      </ul>
                    </div>
                  <% end %>
                </div>
              </div>

              <div class="col-span-1">
                <div class="h-full flex flex-col">
                  <label
                    for="password"
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    Password (if file is protected)
                  </label>
                  <div class="mt-1 flex flex-row items-center space-x-2">
                    <input
                      type="password"
                      id="password"
                      name="password"
                      placeholder="Enter file password"
                      value={
                        if @candidate.nominee_contact &&
                             @candidate.nominee_contact.encrypted_password do
                          Gaia.BeneficialOwners.CandidateImporterAi.decode_password(
                            @candidate.nominee_contact.encrypted_password
                          )
                        else
                          ""
                        end
                      }
                      phx-hook="PasswordToggle"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-gray-300"
                    />
                    <button
                      type="button"
                      id="password-toggle"
                      class="flex items-center pr-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 cursor-pointer"
                      phx-click={
                        JS.toggle(to: "#show-password-icon")
                        |> JS.toggle(to: "#hide-password-icon")
                        |> JS.dispatch("toggle-password", to: "#password")
                      }
                    >
                      <span id="show-password-icon">
                        <PC.icon name="hero-eye" class="h-5 w-5" />
                      </span>
                      <span id="hide-password-icon" class="hidden">
                        <PC.icon name="hero-eye-slash" class="h-5 w-5" />
                      </span>
                    </button>
                  </div>
                  <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    Enter the password only if your file is password protected (.xlsx, .xls, and .pdf files).
                  </p>

                  <div class="mt-2">
                    <label
                      for="custom_prompt"
                      class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >
                      Custom prompt
                    </label>
                    <textarea
                      id="custom_prompt"
                      name="custom_prompt"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-gray-300"
                    ><%= if @candidate.nominee_contact && @candidate.nominee_contact.custom_prompt do %><%= @candidate.nominee_contact.custom_prompt %><% else %><%= "" %><% end %></textarea>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                      To make the best of the AI please provide an example of your request:
                      "The account_name field should be the concatenation of the CLIENT NAME and FUND NAME columns. e.g., (The NT Company - NT AU BANK HELD)"
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex justify-end pt-2">
              <button
                type="submit"
                class="inline-flex items-center px-3 py-1.5 text-sm font-medium rounded text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                disabled={length(@uploads.document.entries) == 0 || @processing}
              >
                <PC.icon name="hero-arrow-up-tray" class="w-4 h-4 mr-1.5" />
                <span>Upload</span>
              </button>
            </div>
          </form>
        </div>
      </td>
    </tr>
    """
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel_upload", %{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :document, ref)}
  end

  @impl true
  def handle_event("save_document", params, socket) do
    socket =
      socket
      |> assign(:password, params["password"])
      |> assign(:custom_prompt, params["custom_prompt"])
      |> assign(:processing, true)

    consume_uploaded_entries(socket, :document, fn %{path: path}, entry ->
      object_name =
        "uploads/company_profile/#{socket.assigns.company_profile_id}/beneficial_owners/reports/disclosed_nominee_documents/#{format_filename(entry.client_name)}"

      local_path = Path.expand(path)

      {:ok, _} = GoogleAPI.Storage.put_object(object_name, local_path)

      file_type = determine_file_type(entry.client_name)

      upload = %Plug.Upload{
        path: path,
        filename: entry.client_name,
        content_type: entry.client_type
      }

      file_content = get_upload_data(upload, socket.assigns.password, file_type)

      id = self()
      send(self(), {:processing_document, :started})

      Task.start(fn ->
        result =
          if entry.client_name == "custom_form.csv" do
            Gaia.BeneficialOwners.CandidateImporter.import_candidates(
              upload,
              socket.assigns.candidate.report_id,
              socket.assigns.candidate.id
            )
          else
            Gaia.BeneficialOwners.CandidateImporterAi.extract_and_import_candidates(
              file_content,
              socket.assigns.candidate.report_id,
              socket.assigns.candidate.id,
              socket.assigns.custom_prompt,
              socket.assigns.is_uk,
              file_type
            )
          end

        case result do
          {:ok, %{candidates_count: count, has_errors: true}} ->
            send(
              id,
              {:flash, :error,
               "Document processed successfully. #{count} candidates imported. However, some candidates were not imported due to errors. CLICK the refresh button to see the candidates."}
            )

          {:ok, %{candidates_count: count, has_errors: false}} ->
            send(
              id,
              {:flash, :info,
               "Document processed successfully. #{count} candidates imported. CLICK the refresh button to see the candidates."}
            )

          {:ok,
           %{
             parsed_rows_length: parsed_rows_length,
             failed_rows_length: failed_rows_length,
             imported_candidates_length: imported_candidates_length
           }} ->
            send(
              id,
              {:flash, :info,
               "Custom document processed successfully. #{imported_candidates_length} candidates imported. #{parsed_rows_length} rows parsed, #{failed_rows_length} rows failed. CLICK the refresh button to see the candidates."}
            )

          {:error, error} ->
            send(id, {:flash, :error, "Failed to process document: #{error}"})
        end

        send(id, {:processing_document, :finished})
      end)
    end)

    close_form(socket)

    {:noreply, socket}
  end

  defp determine_file_type(filename) do
    case Path.extname(filename) do
      ".csv" ->
        :csv

      ".xlsx" ->
        :xlsx

      ".xls" ->
        :xls

      _ ->
        :pdf
    end
  end

  defp error_to_string(:too_large), do: "File is too large (max 10MB)"
  defp error_to_string(:too_many_files), do: "Too many files (max 5)"
  defp error_to_string(:not_accepted), do: "Invalid file type (must be PDF, CSV, XLSX, or XLS)"

  defp get_upload_data(upload, password, file_type) do
    case upload.filename do
      "custom_form.csv" ->
        upload.path

      _ ->
        {:ok, file_content} =
          Gaia.BeneficialOwners.CandidateImporterAi.extract_upload_data(
            upload,
            password,
            file_type
          )

        file_content
    end
  end

  defp close_form(socket) do
    socket =
      assign(socket, :processing, false)

    send_update(
      socket.assigns.parent_id,
      action: :close_form,
      parent_id: socket.assigns.candidate.id
    )

    {:ok, socket}
  end

  defp format_filename("custom_form.csv") do
    timestamp = Calendar.strftime(NaiveDateTime.utc_now(), "%Y%m%dT%H%M%S")
    "custom_form_#{timestamp}.csv"
  end

  defp format_filename(filename) do
    filename
  end
end
