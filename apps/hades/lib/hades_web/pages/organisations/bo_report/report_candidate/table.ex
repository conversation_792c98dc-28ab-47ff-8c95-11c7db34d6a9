defmodule HadesWeb.Pages.Organisations.BoReport.ReportCandidate.Table do
  @moduledoc """
  Table for BO report candidates
  """

  use HadesWeb, :live_component

  import PetalCom<PERSON>.Dropdown

  alias HadesWeb.Pages.Organisations.BoReport.ReportCandidate.AddressForm
  alias HadesWeb.Pages.Organisations.BoReport.ReportCandidate.EmailForm
  alias HadesWeb.Pages.Organisations.BoReport.ReportCandidate.LinkContactForm
  alias HadesWeb.Pages.Organisations.BoReport.ReportCandidate.NomineeForm
  alias HadesWeb.Pages.Organisations.BoReport.ReportCandidate.PrivateCompanyForm
  alias HadesWeb.Pages.Organisations.BoReport.ReportCandidate.ReassignParentForm

  @impl true
  def update(%{action: :close_form, parent_id: parent_id} = _assigns, socket) do
    # Convert parent_id to integer if it's a string
    parent_id_int = if is_binary(parent_id), do: String.to_integer(parent_id), else: parent_id

    # Add the parent to expanded_parents to show the newly added children
    expanded_parents = Map.put(socket.assigns.expanded_parents, parent_id_int, true)

    socket =
      socket
      |> assign(:expanded_row_id, nil)
      |> assign(:expanded_action_type, nil)
      |> assign(:expanded_parents, expanded_parents)

    {:ok, socket}
  end

  @impl true
  def update(%{action: :close_form} = _assigns, socket) do
    socket =
      socket
      |> assign(:expanded_row_id, nil)
      |> assign(:expanded_action_type, nil)

    {:ok, socket}
  end

  @impl true
  def update(%{action: :collapse_all} = _assigns, socket) do
    # Clear the expanded_parents map to collapse everything
    socket = assign(socket, :expanded_parents, %{})

    # Preserve static indices if they exist
    socket =
      if Map.has_key?(socket.assigns, :static_indices) do
        socket
      else
        # Calculate static indices if they don't exist yet
        static_indices = calculate_static_indices(socket.assigns.all_report_candidates)
        assign(socket, :static_indices, static_indices)
      end

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    # Preserve expanded_parents if it exists in the socket
    expanded_parents =
      if Map.has_key?(socket.assigns, :expanded_parents) do
        socket.assigns.expanded_parents
      else
        # Default to empty map (all collapsed) if not present
        %{}
      end

    # The original update callback for normal assigns
    socket =
      socket
      |> assign(assigns)
      |> assign(:expanded_row_id, nil)
      |> assign(:expanded_action_type, nil)
      |> assign(:company_profile_id, assigns.company_profile.id)
      |> assign(:editing_candidate_id, nil)
      |> assign(:edit_form, %{account_name: "", shares: 0, depot: ""})
      # Track which parents are expanded - preserve existing expanded state
      |> assign(:expanded_parents, expanded_parents)
      |> assign(:is_uk, assigns.company_profile.ticker.market_key in [:lse, :aqse])

    # Calculate static indices for all candidates
    static_indices = calculate_static_indices(assigns.all_report_candidates)
    socket = assign(socket, :static_indices, static_indices)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.candidates_table
        all_report_candidates={@all_report_candidates}
        myself={@myself}
        expanded_row_id={@expanded_row_id}
        expanded_action_type={@expanded_action_type}
        editing_candidate_id={@editing_candidate_id}
        edit_form={@edit_form}
        authorisation_letters={@authorisation_letters}
        expanded_parents={@expanded_parents}
        company_profile_id={@company_profile_id}
        company_profile={@company_profile}
        status_filter={@status_filter}
        type_filter={@type_filter}
        sent_filter={@sent_filter}
        is_uk={@is_uk}
      />
    </div>
    """
  end

  # Helper function to calculate static indices for all candidates
  defp calculate_static_indices(candidates) do
    candidates |> do_calculate_static_indices(%{}, 1) |> elem(0)
  end

  defp do_calculate_static_indices([], acc, idx), do: {acc, idx}

  defp do_calculate_static_indices([candidate | rest], acc, idx) do
    acc = Map.put(acc, candidate.id, idx)
    idx = idx + 1

    {acc, idx} =
      if candidate.children && length(candidate.children) > 0 do
        do_calculate_static_indices(candidate.children, acc, idx)
      else
        {acc, idx}
      end

    do_calculate_static_indices(rest, acc, idx)
  end

  def candidates_table(assigns) do
    # Calculate static indices for all candidates
    static_indices = calculate_static_indices(assigns.all_report_candidates)
    assigns = assign(assigns, :static_indices, static_indices)

    ~H"""
    <div class="overflow-x-auto min-h-[400px]">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th
              scope="col"
              class="w-12 px-2 text-center text-xs font-medium uppercase tracking-wider dark:text-gray-300"
            >
              #
            </th>
            <th
              scope="col"
              class="pl-1 py-3 text-left text-xs font-medium uppercase tracking-wider dark:text-gray-300 w-[200px]"
            >
              Account Name
            </th>
            <%= if @is_uk do %>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider dark:text-gray-300 w-[80px]"
              >
                Depot
              </th>
            <% end %>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider dark:text-gray-300 w-[80px]"
            >
              Previous
            </th>

            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider dark:text-gray-300 w-[100px]"
            >
              Shares
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider dark:text-gray-300 w-[200px]"
            >
              Address
            </th>
            <th
              scope="col"
              class="px-3 py-3 text-center text-xs font-medium uppercase tracking-wider dark:text-gray-300 w-[60px]"
            >
              <div class="flex items-center justify-center">
                <.dropdown class="w-auto" menu_items_wrapper_class="w-28 py-0.5">
                  <:trigger_element>
                    <div class="flex items-center justify-center cursor-pointer">
                      <span class="mr-1">
                        <%= if @sent_filter == "true" do %>
                          Sent
                        <% else %>
                          All
                        <% end %>
                      </span>
                      <PC.icon name="hero-funnel" class="w-3 h-3 text-gray-500" />
                    </div>
                  </:trigger_element>
                  <.dropdown_menu_item
                    phx-click="update_sent_filter"
                    phx-value-sent=""
                    phx-target={@myself}
                    class={
                      if is_nil(@sent_filter) && is_nil(@type_filter) && is_nil(@status_filter),
                        do: "bg-gray-100 dark:bg-gray-700 py-1",
                        else: "py-1"
                    }
                  >
                    <div class="flex items-center space-x-1.5">
                      <PC.icon name="hero-funnel" class="w-4 h-4 text-gray-500" />
                      <span class="text-sm text-gray-700 dark:text-gray-300">All</span>
                    </div>
                  </.dropdown_menu_item>
                  <.dropdown_menu_item
                    phx-click="update_sent_filter"
                    phx-value-sent="true"
                    phx-target={@myself}
                    class={
                      if @sent_filter == "true",
                        do: "bg-gray-100 dark:bg-gray-700 py-1",
                        else: "py-1"
                    }
                  >
                    <div class="flex items-center space-x-1.5">
                      <PC.icon name="hero-check" class="w-4 h-4 text-green-500" />
                      <span class="text-sm text-green-500">Sent</span>
                    </div>
                  </.dropdown_menu_item>
                </.dropdown>
              </div>
            </th>
            <th
              scope="col"
              class="px-3 py-3 text-center text-xs font-medium uppercase tracking-wider dark:text-gray-300 w-[60px]"
            >
              <div class="flex items-center justify-center">
                <.dropdown class="w-auto" menu_items_wrapper_class="w-28 py-0.5">
                  <:trigger_element>
                    <div class="flex items-center justify-center cursor-pointer">
                      <span class="mr-1">
                        <%= if is_nil(@type_filter) || @type_filter == "" do %>
                          Type
                        <% else %>
                          {@type_filter |> to_string() |> String.capitalize()}
                        <% end %>
                      </span>
                      <PC.icon name="hero-funnel" class="w-3 h-3 text-gray-500" />
                    </div>
                  </:trigger_element>
                  <.dropdown_menu_item
                    phx-click="update_type_filter"
                    phx-value-type=""
                    phx-target={@myself}
                    class={
                      if is_nil(@type_filter) && is_nil(@status_filter),
                        do: "bg-gray-100 dark:bg-gray-700 py-1",
                        else: "py-1"
                    }
                  >
                    <div class="flex items-center space-x-1.5">
                      <PC.icon name="hero-funnel" class="w-4 h-4 text-gray-500" />
                      <span class="text-sm text-gray-700 dark:text-gray-300">All</span>
                    </div>
                  </.dropdown_menu_item>
                  <%= for type <- Gaia.BeneficialOwners.ReportCandidate.types() do %>
                    <.dropdown_menu_item
                      phx-click="update_type_filter"
                      phx-value-type={type}
                      phx-target={@myself}
                      class={
                        if @type_filter == type,
                          do: "bg-gray-100 dark:bg-gray-700 py-1",
                          else: "py-1"
                      }
                    >
                      <div class="flex items-center space-x-1.5">
                        <PC.icon name={type_icon(type)} class={"w-4 h-4 #{type_color(type)}"} />
                        <span class={"text-sm #{type_color(type)}"}>
                          {type
                          |> to_string()
                          |> String.capitalize()}
                        </span>
                      </div>
                    </.dropdown_menu_item>
                  <% end %>
                </.dropdown>
              </div>
            </th>
            <th
              scope="col"
              class="px-3 py-3 text-center text-xs font-medium uppercase tracking-wider dark:text-gray-300 w-[60px]"
            >
              <div class="flex items-center justify-center">
                <.dropdown class="w-auto" menu_items_wrapper_class="w-28 py-0.5">
                  <:trigger_element>
                    <div class="flex items-center justify-center cursor-pointer">
                      <span class="mr-1">
                        <%= if is_nil(@status_filter) || @status_filter == "" do %>
                          Status
                        <% else %>
                          {@status_filter |> to_string() |> String.capitalize()}
                        <% end %>
                      </span>
                      <PC.icon name="hero-funnel" class="w-3 h-3 text-gray-500" />
                    </div>
                  </:trigger_element>
                  <.dropdown_menu_item
                    phx-click="update_status_filter"
                    phx-value-status=""
                    phx-target={@myself}
                    class={
                      if is_nil(@type_filter) && is_nil(@status_filter),
                        do: "bg-gray-100 dark:bg-gray-700 py-1",
                        else: "py-1"
                    }
                  >
                    <div class="flex items-center space-x-1.5">
                      <PC.icon name="hero-funnel" class="w-4 h-4 text-gray-500" />
                      <span class="text-sm text-gray-700 dark:text-gray-300">All</span>
                    </div>
                  </.dropdown_menu_item>
                  <%= for status <- Gaia.BeneficialOwners.ReportCandidate.statuses() do %>
                    <.dropdown_menu_item
                      phx-click="update_status_filter"
                      phx-value-status={status}
                      phx-target={@myself}
                      class={
                        if @status_filter == status,
                          do: "bg-gray-100 dark:bg-gray-700 py-1",
                          else: "py-1"
                      }
                    >
                      <div class="flex items-center space-x-1.5">
                        <PC.icon name={status_icon(status)} class={"w-4 h-4 #{status_color(status)}"} />
                        <span class={"text-sm #{status_color(status)}"}>
                          {status
                          |> to_string()
                          |> String.capitalize()}
                        </span>
                      </div>
                    </.dropdown_menu_item>
                  <% end %>
                </.dropdown>
              </div>
            </th>
            <th scope="col" class="px-2 py-3 text-center w-[50px]"></th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
          <%= for {parent, _} <- Enum.with_index(@all_report_candidates, 1) do %>
            <%= if parent.layer == 1 do %>
              <.candidate_row
                candidate={parent}
                layer={1}
                row_number={Map.get(@static_indices, parent.id)}
                myself={@myself}
                expanded_row_id={@expanded_row_id}
                expanded_action_type={@expanded_action_type}
                editing_candidate_id={@editing_candidate_id}
                edit_form={@edit_form}
                authorisation_letters={@authorisation_letters}
                expanded_parents={@expanded_parents}
                company_profile_id={@company_profile_id}
                company_profile={@company_profile}
                status_filter={@status_filter}
                type_filter={@type_filter}
                sent_filter={@sent_filter}
                static_indices={@static_indices}
                is_uk={@is_uk}
              />

              <%= if parent.children && length(parent.children) > 0 && is_expanded?(parent.id, @expanded_parents) do %>
                <%= for {child, index} <- Enum.with_index(parent.children) do %>
                  <% is_last_child = index == length(parent.children) - 1 %>
                  <.candidate_row
                    candidate={child}
                    layer={2}
                    row_number={Map.get(@static_indices, child.id)}
                    myself={@myself}
                    expanded_row_id={@expanded_row_id}
                    expanded_action_type={@expanded_action_type}
                    editing_candidate_id={@editing_candidate_id}
                    edit_form={@edit_form}
                    authorisation_letters={@authorisation_letters}
                    expanded_parents={@expanded_parents}
                    company_profile_id={@company_profile_id}
                    company_profile={@company_profile}
                    status_filter={@status_filter}
                    type_filter={@type_filter}
                    sent_filter={@sent_filter}
                    static_indices={@static_indices}
                    is_last_child={is_last_child}
                    is_uk={@is_uk}
                  />

                  <%= if child.children && length(child.children) > 0 && is_expanded?(child.id, @expanded_parents) do %>
                    <%= for {grandchild, gindex} <- Enum.with_index(child.children) do %>
                      <% is_last_grandchild = gindex == length(child.children) - 1 %>
                      <.candidate_row
                        candidate={grandchild}
                        layer={3}
                        row_number={Map.get(@static_indices, grandchild.id)}
                        myself={@myself}
                        expanded_row_id={@expanded_row_id}
                        expanded_action_type={@expanded_action_type}
                        editing_candidate_id={@editing_candidate_id}
                        edit_form={@edit_form}
                        authorisation_letters={@authorisation_letters}
                        expanded_parents={@expanded_parents}
                        company_profile_id={@company_profile_id}
                        company_profile={@company_profile}
                        status_filter={@status_filter}
                        type_filter={@type_filter}
                        sent_filter={@sent_filter}
                        static_indices={@static_indices}
                        is_last_child={is_last_grandchild}
                        is_uk={@is_uk}
                      />
                    <% end %>
                  <% end %>
                <% end %>
              <% end %>
            <% end %>
          <% end %>
        </tbody>
      </table>
    </div>
    """
  end

  def candidate_row(assigns) do
    # Ensure static_indices is available in assigns
    assigns = assign_new(assigns, :static_indices, fn -> %{} end)
    # Ensure is_last_child is available in assigns
    assigns = assign_new(assigns, :is_last_child, fn -> false end)

    ~H"""
    <tr>
      <td class="w-12 px-2 text-center text-xs text-gray-400 dark:text-gray-500">
        <div class="flex items-center justify-center gap-1">
          <%= if @candidate.children && length(@candidate.children) > 0 do %>
            <button
              phx-click="toggle_children"
              phx-value-id={@candidate.id}
              phx-target={@myself}
              class="flex items-center gap-1 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none transition-colors"
            >
              <span>{@row_number}</span>
              <%= if is_expanded?(@candidate.id, @expanded_parents) do %>
                <PC.icon name="hero-minus" class="w-3 h-3" />
              <% else %>
                <PC.icon name="hero-plus" class="w-3 h-3" />
              <% end %>
            </button>
          <% else %>
            <span>{@row_number}</span>
          <% end %>
        </div>
      </td>
      <td class="whitespace-nowrap text-sm dark:text-gray-300 pl-1 relative">
        <%= if @layer > 1 do %>
          {raw(hierarchy_line_indicator(@layer, @is_last_child))}
          <div class={"absolute left-0 top-[50%] w-4 border-t border-indigo-400 dark:border-indigo-600 ml-#{if @layer == 2, do: "2", else: "8"}"}>
          </div>
        <% end %>
        <div class={name_cell_class(@layer)}>
          <%= if @editing_candidate_id == @candidate.id do %>
            <form
              phx-change="update_edit_field"
              phx-submit="prevent_default"
              phx-target={@myself}
              class="w-full"
            >
              <div class="inline-edit">
                <input
                  type="text"
                  name="account_name"
                  value={@edit_form.account_name}
                  class="bg-transparent border-0 border-b border-dashed border-gray-400 focus:border-indigo-500 focus:ring-0 w-full text-sm dark:text-gray-300 p-0 m-0"
                  x-init="if (!document.activeElement || document.activeElement === document.body) { $el.focus(); $el.setSelectionRange($el.value.length, $el.value.length); }"
                  phx-keydown="keydown"
                  phx-value-id={@candidate.id}
                  phx-value-field="account_name"
                  phx-value-value={@edit_form.account_name}
                  phx-target={@myself}
                />
              </div>
            </form>
          <% else %>
            <div class="flex items-baseline">
              <div
                class="cursor-pointer hover:text-indigo-600 min-h-[24px] max-w-[180px] leading-tight"
                phx-click="start_edit"
                phx-value-id={@candidate.id}
                phx-target={@myself}
              >
                <%= if is_nil(@candidate.account_name) || @candidate.account_name == "" do %>
                  <span class="text-gray-400 italic">Click to edit</span>
                <% else %>
                  <div class="flex items-center gap-1">
                    <span class="line-clamp-2" title={@candidate.account_name}>
                      {@candidate.account_name}
                    </span>
                    <%= if @candidate.children && length(@candidate.children) > 0 do %>
                      <span class="inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                        {length(@candidate.children)}
                      </span>
                    <% end %>
                  </div>
                  <%= if String.length(@candidate.account_name) > 25 do %>
                    <span class="text-xs text-gray-500 italic block mt-1">
                      Click to view full name
                    </span>
                  <% end %>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </td>

      <%= if @is_uk do %>
        <td class="whitespace-nowrap text-sm dark:text-gray-300">
          <%= if @candidate.layer != 1 do %>
            <%= if @editing_candidate_id == @candidate.id do %>
              <form phx-change="update_edit_field" phx-target={@myself} class="w-full">
                <input
                  type="text"
                  name="depot"
                  value={@edit_form.depot}
                  class="bg-transparent border-0 border-b border-dashed border-gray-400 focus:border-indigo-500 focus:ring-0 w-full text-sm dark:text-gray-300 p-0 m-0"
                />
              </form>
            <% else %>
              <div class="flex items-baseline">
                <div
                  class="cursor-pointer hover:text-indigo-600 min-h-[24px] max-w-[180px] leading-tight"
                  phx-click="start_edit"
                  phx-value-id={@candidate.id}
                  phx-target={@myself}
                >
                  <%= if is_nil(@candidate.depot) || @candidate.depot == "" do %>
                    <span class="text-gray-400 italic">Click to edit</span>
                  <% else %>
                    <span class="line-clamp-2" title={@candidate.depot}>{@candidate.depot}</span>
                  <% end %>
                </div>
              </div>
            <% end %>
          <% else %>
            <span class="text-gray-400 italic">-</span>
          <% end %>
        </td>
      <% end %>

      <td class="whitespace-nowrap text-sm dark:text-gray-300">
        <%= if (@candidate.last_report_candidate && @candidate.last_report_candidate.shares) || @candidate.last_report_candidate_shares do %>
          <div class="flex items-center">
            <span class="font-medium text-gray-400 dark:text-gray-500">
              {format_number_with_commas(
                (@candidate.last_report_candidate_shares > 0 &&
                   @candidate.last_report_candidate_shares) ||
                  (@candidate.last_report_candidate && @candidate.last_report_candidate.shares) || 0
              )}
            </span>
          </div>
        <% else %>
          <span class="text-gray-400 italic">-</span>
        <% end %>
      </td>

      <td class="whitespace-nowrap text-sm dark:text-gray-300">
        <%= if @editing_candidate_id == @candidate.id do %>
          <form phx-change="update_edit_field" phx-target={@myself} class="w-full">
            <div class="inline-edit">
              <input
                type="number"
                name="shares"
                value={@edit_form.shares}
                class="bg-transparent border-0 border-b border-dashed border-gray-400 focus:border-indigo-500 focus:ring-0 w-full text-sm dark:text-gray-300 p-0 m-0"
                phx-keydown="keydown"
                phx-value-id={@candidate.id}
                phx-value-field="shares"
                phx-value-value={@edit_form.shares}
                phx-target={@myself}
              />
            </div>
          </form>
        <% else %>
          <div
            class="cursor-pointer hover:text-indigo-600"
            phx-click="start_edit"
            phx-value-id={@candidate.id}
            phx-target={@myself}
          >
            <div class="flex items-center">
              <span class="font-medium">{format_number_with_commas(@candidate.shares)}</span>

              <div class="ml-1.5 flex flex-col space-y-0">
                <%= cond do %>
                  <% @layer > 1 && @candidate.parent && @candidate.parent.shares && @candidate.parent.shares > 0 && @candidate.shares > 0 -> %>
                    <% parent_percentage =
                      calculate_percentage(@candidate.shares, @candidate.parent.shares) %>
                    <span class={"text-[10px] flex items-center #{percentage_color_class(parent_percentage)}"}>
                      <PC.icon name="hero-arrow-turn-left-up" class="w-2 h-2 mr-0.5" />
                      {:erlang.float_to_binary(parent_percentage, decimals: 1)}%
                    </span>

                    <%= if @layer == 2 && @candidate.children && length(@candidate.children) > 0 && @candidate.shares > 0 do %>
                      <% total_child_shares =
                        Enum.reduce(@candidate.children, 0, fn child, acc ->
                          acc + (child.shares || 0)
                        end) %>
                      <% child_percentage =
                        calculate_percentage(total_child_shares, @candidate.shares) %>
                      <span class={"text-[10px] flex items-center #{percentage_color_class(child_percentage)}"}>
                        <PC.icon name="hero-arrow-turn-right-down" class="w-2 h-2 mr-0.5" />
                        {:erlang.float_to_binary(child_percentage, decimals: 1)}%
                      </span>
                    <% end %>
                  <% @candidate.children && length(@candidate.children) > 0 && @candidate.shares > 0 -> %>
                    <% total_child_shares =
                      Enum.reduce(@candidate.children, 0, fn child, acc ->
                        acc + (child.shares || 0)
                      end) %>
                    <% percentage = calculate_percentage(total_child_shares, @candidate.shares) %>
                    <span class={"text-[10px] flex items-center #{percentage_color_class(percentage)}"}>
                      <PC.icon name="hero-arrow-turn-right-down" class="w-2 h-2 mr-0.5" />
                      {:erlang.float_to_binary(percentage, decimals: 1)}%
                    </span>
                  <% true -> %>
                    <span></span>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </td>

      <td class="whitespace-nowrap text-sm dark:text-gray-300">
        <div
          class="cursor-pointer hover:text-indigo-600"
          phx-click="toggle_action"
          phx-value-id={@candidate.id}
          phx-value-action="address"
          phx-target={@myself}
        >
          <%= if has_address?(@candidate) do %>
            <div class="text-xs text-gray-600 dark:text-gray-400 leading-tight max-w-[180px]">
              <span class="line-clamp-2" title={format_address_line_1(@candidate)}>
                {format_address_line_1(@candidate)}
              </span>
              <%= if has_address_line_2?(@candidate) do %>
                <div
                  class="text-xs text-gray-500 dark:text-gray-500 mt-0.5 line-clamp-2"
                  title={format_address_line_2(@candidate)}
                >
                  {format_address_line_2(@candidate)}
                </div>
              <% end %>
            </div>
          <% else %>
            <span class="text-xs text-gray-400 italic">Click to add address</span>
          <% end %>
        </div>
      </td>

      <td class="whitespace-nowrap text-sm dark:text-gray-300 text-center">
        <%= if @candidate.last_contact_at do %>
          <PC.icon name="hero-check" class="w-5 h-5 text-green-500 dark:text-green-400 inline-block" />
        <% else %>
          -
        <% end %>
      </td>

      <td class="whitespace-nowrap text-sm dark:text-gray-300">
        <.type_dropdown candidate={@candidate} myself={@myself} />
      </td>

      <td class="whitespace-nowrap text-sm dark:text-gray-300">
        <.status_dropdown candidate={@candidate} myself={@myself} />
      </td>

      <td class="whitespace-nowrap text-sm dark:text-gray-300 px-2 text-center">
        <.candidate_actions
          candidate={@candidate}
          myself={@myself}
          editing={@editing_candidate_id == @candidate.id}
          layer={@layer}
        />
      </td>
    </tr>

    <%= if @expanded_row_id == @candidate.id do %>
      <tr>
        <td colspan="8" class="p-0 border-t-0">
          <%= case @expanded_action_type do %>
            <% :address -> %>
              <.live_component
                module={AddressForm}
                id={"address-form-#{@candidate.id}"}
                candidate={@candidate}
                parent_id={@myself}
              />
            <% :upload_nominee_document -> %>
              <.live_component
                module={NomineeForm}
                id={"upload-form-#{@candidate.id}"}
                candidate={@candidate}
                parent_id={@myself}
                company_profile_id={@company_profile_id}
                is_uk={@is_uk}
              />
            <% :upload_asic_document -> %>
              <.live_component
                module={PrivateCompanyForm}
                id={"upload-form-#{@candidate.id}"}
                candidate={@candidate}
                parent_id={@myself}
              />
            <% :email -> %>
              <.live_component
                module={EmailForm}
                id={"email-form-#{@candidate.id}"}
                candidate={@candidate}
                parent_id={@myself}
                authorisation_letters={@authorisation_letters}
              />
            <% :link_contact -> %>
              <.live_component
                module={LinkContactForm}
                id={"link-contact-form-#{@candidate.id}"}
                candidate={@candidate}
                parent_id={@myself}
              />
            <% :reassign_parent -> %>
              <.live_component
                module={ReassignParentForm}
                id={"reassign-parent-form-#{@candidate.id}"}
                candidate={@candidate}
                parent_id={@myself}
                company_profile={@company_profile}
              />
            <% _ -> %>
          <% end %>
        </td>
      </tr>
    <% end %>
    """
  end

  def candidate_actions(assigns) do
    ~H"""
    <div class="flex items-center justify-center">
      <%= if @editing do %>
        <.edit_actions candidate={@candidate} myself={@myself} />
      <% else %>
        <div x-data="{ open: false }" class="relative">
          <button
            @click="open = !open"
            @keydown.escape.window="open = false"
            @click.away="open = false"
            class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title="Actions"
          >
            <PC.icon name="hero-ellipsis-horizontal" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>

          <div
            x-cloak
            x-show="open"
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="transform opacity-0 scale-95"
            x-transition:enter-end="transform opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-75"
            x-transition:leave-start="transform opacity-100 scale-100"
            x-transition:leave-end="transform opacity-0 scale-95"
            class="absolute flex flex-col w-48 mt-1 shadow-lg rounded-md bg-white dark:bg-gray-800 z-50 right-0 ring-1 ring-black ring-opacity-5 dark:ring-gray-700"
          >
            <%= cond do %>
              <% @candidate.type == :nominee -> %>
                <%= if @candidate.nominee_contact_id && @candidate.layer < 3 do %>
                  <%= if @candidate.nominee_contact do %>
                    <.send_nominee_contact_email_button candidate={@candidate} myself={@myself} />
                  <% end %>
                  <.unlink_nominee_contact_button candidate={@candidate} myself={@myself} />
                <% end %>
                <%= if @candidate.layer < 3 && !@candidate.nominee_contact_id do %>
                  <.link_nominee_contact_button candidate={@candidate} myself={@myself} />
                <% end %>
                <%= if @candidate.nominee_contact do %>
                  <.view_nominee_contact_button candidate={@candidate} myself={@myself} />
                <% end %>
                <%= if @candidate.layer < 3 do %>
                  <.upload_nominee_document_button candidate={@candidate} myself={@myself} />
                  <.add_new_candidate_button candidate={@candidate} myself={@myself} />
                  <.delete_all_children_button candidate={@candidate} myself={@myself} />
                <% end %>
                <%= if @candidate.layer > 1 do %>
                  <.reassign_parent_button candidate={@candidate} myself={@myself} />
                <% end %>
                <.delete_candidate_button candidate={@candidate} myself={@myself} />
              <% @candidate.type == :asic -> %>
                <.upload_asic_document_button candidate={@candidate} myself={@myself} />
                <%= if @candidate.layer < 3 do %>
                  <.add_new_candidate_button candidate={@candidate} myself={@myself} />
                <% end %>
                <%= if @candidate.layer > 1 do %>
                  <.reassign_parent_button candidate={@candidate} myself={@myself} />
                <% end %>
                <.delete_candidate_button candidate={@candidate} myself={@myself} />
              <% @candidate.type == :owner -> %>
                <%= if @candidate.layer > 1 do %>
                  <.reassign_parent_button candidate={@candidate} myself={@myself} />
                <% end %>
                <.delete_candidate_button candidate={@candidate} myself={@myself} />
              <% true -> %>
                <.add_new_candidate_button candidate={@candidate} myself={@myself} />
                <%= if @candidate.layer > 1 do %>
                  <.reassign_parent_button candidate={@candidate} myself={@myself} />
                <% end %>
                <.delete_candidate_button candidate={@candidate} myself={@myself} />
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  def edit_actions(assigns) do
    ~H"""
    <button
      type="button"
      phx-click="save_edit"
      phx-value-id={@candidate.id}
      phx-target={@myself}
      class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 transition-colors"
      title="Save"
    >
      <PC.icon name="hero-check" class="w-4 h-4" />
    </button>
    <button
      type="button"
      phx-click="cancel_edit"
      phx-target={@myself}
      class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
      title="Cancel"
    >
      <PC.icon name="hero-x-mark" class="w-4 h-4" />
    </button>
    """
  end

  def send_nominee_contact_email_button(assigns) do
    ~H"""
    <div
      class="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
      phx-click="toggle_action"
      phx-value-id={@candidate.id}
      phx-value-action="email"
      phx-target={@myself}
    >
      <div class="flex items-center gap-2">
        <PC.icon name="hero-envelope" class="w-4 h-4" />
        <span class="text-gray-800 dark:text-gray-200">Send Email</span>
      </div>
    </div>
    """
  end

  def link_nominee_contact_button(assigns) do
    ~H"""
    <div
      class="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
      phx-click="toggle_action"
      phx-value-id={@candidate.id}
      phx-value-action="link_contact"
      phx-target={@myself}
    >
      <div class="flex items-center gap-2">
        <PC.icon name="hero-link" class="w-4 h-4" />
        <span class="text-gray-800 dark:text-gray-200">Link Contact</span>
      </div>
    </div>
    """
  end

  def unlink_nominee_contact_button(assigns) do
    ~H"""
    <div
      class="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
      phx-click="unlink_contact"
      phx-value-id={@candidate.id}
      phx-target={@myself}
      data-confirm="Are you sure you want to unlink this contact?"
    >
      <div class="flex items-center gap-2">
        <PC.icon name="hero-link-slash" class="w-4 h-4" />
        <span class="text-gray-800 dark:text-gray-200">Unlink Contact</span>
      </div>
    </div>
    """
  end

  def upload_nominee_document_button(assigns) do
    ~H"""
    <div
      class="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
      phx-click="toggle_action"
      phx-value-id={@candidate.id}
      phx-value-action="upload_nominee_document"
      phx-target={@myself}
    >
      <div class="flex items-center gap-2">
        <PC.icon name="hero-arrow-up-tray" class="w-4 h-4" />
        <span class="text-gray-800 dark:text-gray-200">Upload Document</span>
      </div>
    </div>
    """
  end

  def upload_asic_document_button(assigns) do
    ~H"""
    <div
      class="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
      phx-click="toggle_action"
      phx-value-id={@candidate.id}
      phx-value-action="upload_asic_document"
      phx-target={@myself}
    >
      <div class="flex items-center gap-2">
        <PC.icon name="hero-arrow-up-tray" class="w-4 h-4" />
        <span class="text-gray-800 dark:text-gray-200">Upload ASIC Document</span>
      </div>
    </div>
    """
  end

  def add_new_candidate_button(assigns) do
    ~H"""
    <div
      class="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
      phx-click="toggle_action"
      phx-value-id={@candidate.id}
      phx-value-action="add"
      phx-value-report_id={@candidate.report_id}
      phx-value-layer={@candidate.layer}
      phx-target={@myself}
    >
      <div class="flex items-center gap-2">
        <PC.icon name="hero-plus-circle" class="w-4 h-4" />
        <span class="text-gray-800 dark:text-gray-200">Add Child</span>
      </div>
    </div>
    """
  end

  def view_nominee_contact_button(assigns) do
    ~H"""
    <div
      class="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
      phx-click="toggle_action"
      phx-value-id={@candidate.id}
      phx-value-action="view_nominee_contact"
      phx-target={@myself}
    >
      <.link
        href={"/beneficial_owners/nominee_contacts/#{@candidate.nominee_contact.id}"}
        target="_blank"
        class="flex items-center gap-2"
      >
        <div class="flex items-center gap-2">
          <PC.icon name="hero-eye" class="w-4 h-4" />
          <span class="text-gray-800 dark:text-gray-200">View Nominee Contact</span>
        </div>
      </.link>
    </div>
    """
  end

  def delete_all_children_button(assigns) do
    ~H"""
    <div
      class="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
      phx-click="toggle_action"
      phx-value-id={@candidate.id}
      phx-value-action="delete_all_children"
      phx-target={@myself}
      data-confirm="Are you sure you want to delete children without last report reference? Children from previous reports will be preserved."
    >
      <div class="flex items-center gap-2">
        <PC.icon name="hero-trash" class="w-4 h-4 text-red-600 dark:text-red-400" />
        <span class="text-red-600 dark:text-red-400">Delete New Children</span>
      </div>
    </div>
    """
  end

  def reassign_parent_button(assigns) do
    ~H"""
    <div
      class="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
      phx-click="toggle_action"
      phx-value-id={@candidate.id}
      phx-value-action="reassign_parent"
      phx-target={@myself}
    >
      <div class="flex items-center gap-2">
        <PC.icon name="hero-arrows-right-left" class="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
        <span class="text-gray-800 dark:text-gray-200">Reassign Parent</span>
      </div>
    </div>
    """
  end

  def delete_candidate_button(assigns) do
    ~H"""
    <div
      class="p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
      phx-click="toggle_action"
      phx-value-id={@candidate.id}
      phx-value-action="delete"
      phx-target={@myself}
      data-confirm="Are you sure you want to delete this candidate?"
    >
      <div class="flex items-center gap-2">
        <PC.icon name="hero-trash" class="w-4 h-4 text-red-600 dark:text-red-400" />
        <span class="text-red-600 dark:text-red-400">Delete</span>
      </div>
    </div>
    """
  end

  def type_dropdown(assigns) do
    ~H"""
    <.dropdown class="w-auto" menu_items_wrapper_class="w-28 py-0.5">
      <:trigger_element>
        <div class="flex items-center justify-center cursor-pointer">
          <PC.icon
            name={type_icon(@candidate.type)}
            class={"w-5 h-5 #{type_color(@candidate.type)}"}
            title={Phoenix.Naming.humanize(@candidate.type)}
          />
        </div>
      </:trigger_element>
      <%= for type <- Gaia.BeneficialOwners.ReportCandidate.types() do %>
        <.dropdown_menu_item
          phx-click="update_type"
          phx-value-id={@candidate.id}
          phx-value-type={type}
          phx-target={@myself}
          class={if type == @candidate.type, do: "bg-gray-100 dark:bg-gray-700 py-1", else: "py-1"}
        >
          <div class="flex items-center space-x-1.5">
            <PC.icon name={type_icon(type)} class={"w-4 h-4 #{type_color(type)}"} />
            <span class={"text-sm #{type_color(type)}"}>
              {type
              |> to_string()
              |> String.capitalize()}
            </span>
          </div>
        </.dropdown_menu_item>
      <% end %>
    </.dropdown>
    """
  end

  def status_dropdown(assigns) do
    ~H"""
    <.dropdown class="w-auto" menu_items_wrapper_class="w-28 py-0.5">
      <:trigger_element>
        <div class="flex items-center justify-center cursor-pointer">
          <PC.icon
            name={status_icon(@candidate.status)}
            class={"w-5 h-5 #{status_color(@candidate.status)}"}
            title={Phoenix.Naming.humanize(@candidate.status)}
          />
        </div>
      </:trigger_element>
      <%= for status <- Gaia.BeneficialOwners.ReportCandidate.statuses() do %>
        <.dropdown_menu_item
          phx-click="update_status"
          phx-value-id={@candidate.id}
          phx-value-status={status}
          phx-target={@myself}
          class={
            if status == @candidate.status, do: "bg-gray-100 dark:bg-gray-700 py-1", else: "py-1"
          }
        >
          <div class="flex items-center space-x-1.5">
            <PC.icon name={status_icon(status)} class={"w-4 h-4 #{status_color(status)}"} />
            <span class={"text-sm #{status_color(status)}"}>
              {status
              |> to_string()
              |> String.capitalize()}
            </span>
          </div>
        </.dropdown_menu_item>
      <% end %>
    </.dropdown>
    """
  end

  @impl true
  def handle_event("start_edit", %{"id" => id}, socket) do
    id = String.to_integer(id)

    candidate =
      Gaia.BeneficialOwners.get_report_candidate!(id)

    if candidate do
      socket =
        socket
        |> assign(:editing_candidate_id, id)
        |> assign(:edit_form, %{
          account_name: candidate.account_name,
          shares: candidate.shares,
          depot: candidate.depot
        })

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("update_edit_field", params, socket) do
    edit_form = socket.assigns.edit_form

    edit_form =
      cond do
        Map.has_key?(params, "account_name") ->
          Map.put(edit_form, :account_name, params["account_name"])

        Map.has_key?(params, "shares") ->
          shares =
            case Integer.parse(params["shares"]) do
              {num, _} -> num
              :error -> 0
            end

          Map.put(edit_form, :shares, shares)

        Map.has_key?(params, "depot") ->
          Map.put(edit_form, :depot, params["depot"])

        true ->
          edit_form
      end

    {:noreply, assign(socket, :edit_form, edit_form)}
  end

  @impl true
  def handle_event("prevent_default", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("keydown", %{"key" => "Enter"} = params, socket) do
    # Extract the necessary values from params
    id = String.to_integer(params["id"])
    save_candidate_edit(id, socket)
  end

  @impl true
  def handle_event("keydown", %{"key" => "Escape"}, socket) do
    {:noreply, assign(socket, :editing_candidate_id, nil)}
  end

  @impl true
  def handle_event("keydown", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("save_edit", %{"id" => id}, socket) do
    id = String.to_integer(id)
    save_candidate_edit(id, socket)
  end

  @impl true
  def handle_event("update_type", %{"id" => id, "type" => type}, socket) do
    id = String.to_integer(id)
    type = String.to_existing_atom(type)
    candidate = Gaia.BeneficialOwners.get_report_candidate!(id)

    attrs =
      case type do
        :owner ->
          %{type: type, status: :done}

        :nominee ->
          %{type: type, status: :pending}

        :asic ->
          %{type: type, status: :pending}

        _ ->
          %{type: type}
      end

    case Gaia.BeneficialOwners.update_report_candidate(candidate, attrs) do
      {:ok, _updated_candidate} ->
        send(self(), {:refresh_candidates})
        {:noreply, socket}

      {:error, _changeset} ->
        send(self(), {:flash, :error, "Failed to update type"})
        {:noreply, socket}
    end
  end

  def handle_event("toggle_action", %{"id" => report_id, "action" => "delete"}, socket) do
    candidate = Gaia.BeneficialOwners.get_report_candidate!(report_id)

    case Gaia.BeneficialOwners.delete_report_candidate(candidate) do
      {:ok, _deleted_candidate} ->
        send(self(), {:refresh_candidates})

        {:noreply, socket}

      {:error, _changeset} ->
        send(self(), {:flash, :error, "Failed to delete candidate"})

        {:noreply, socket}
    end
  end

  def handle_event("toggle_action", %{"id" => report_id, "action" => "delete_all_children"}, socket) do
    candidate = Gaia.BeneficialOwners.get_report_candidate!(report_id)

    # Make sure we're passing an integer ID
    parent_id =
      if is_binary(candidate.id), do: String.to_integer(candidate.id), else: candidate.id

    # Delete children without last_report_candidate_id and set shares to 0 for others
    {:ok, %{deleted_count: deleted_count, updated_count: updated_count}} =
      Gaia.BeneficialOwners.delete_children_without_last_report(parent_id)

    cond do
      deleted_count > 0 && updated_count > 0 ->
        send(
          self(),
          {:flash, :info,
           "Successfully deleted #{deleted_count} children without last report reference and set shares to 0 for #{updated_count} children"}
        )

      deleted_count > 0 ->
        send(
          self(),
          {:flash, :info, "Successfully deleted #{deleted_count} children without last report reference"}
        )

      updated_count > 0 ->
        send(
          self(),
          {:flash, :info, "Successfully set shares to 0 for #{updated_count} children"}
        )

      true ->
        send(self(), {:flash, :info, "No children to process"})
    end

    send(self(), {:refresh_candidates})
    {:noreply, socket}
  end

  def handle_event(
        "toggle_action",
        %{"id" => parent_id, "action" => "add", "report_id" => report_id, "layer" => layer},
        socket
      ) do
    parent_id_int = if is_binary(parent_id), do: String.to_integer(parent_id), else: parent_id

    {:ok, %Gaia.BeneficialOwners.ReportCandidate{} = new_candidate} =
      Gaia.BeneficialOwners.create_report_candidate_with_nominee_or_asic(%{
        layer: layer |> String.to_integer() |> Kernel.+(1),
        report_id: report_id,
        parent_id: parent_id,
        account_name: "",
        shares: 0,
        depot: ""
      })

    send(self(), {:refresh_candidates})

    # Add the parent to expanded_parents to show the newly added child
    expanded_parents = Map.put(socket.assigns.expanded_parents, parent_id_int, true)

    socket =
      socket
      |> assign(:editing_candidate_id, new_candidate.id)
      |> assign(:edit_form, %{
        account_name: new_candidate.account_name || "",
        shares: new_candidate.shares || 0,
        depot: new_candidate.depot || ""
      })
      |> assign(:expanded_parents, expanded_parents)

    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_action", %{"id" => id, "action" => action}, socket) do
    id = String.to_integer(id)
    action_atom = String.to_existing_atom(action)

    # If this row is already expanded with the same action, collapse it
    if socket.assigns.expanded_row_id == id && socket.assigns.expanded_action_type == action_atom do
      {:noreply, socket |> assign(:expanded_row_id, nil) |> assign(:expanded_action_type, nil)}
    else
      # Otherwise, expand this row with the specified action
      case maybe_check_authorisation(action_atom, socket.assigns.authorisation_letters) do
        {:ok, _} ->
          {:noreply, socket |> assign(:expanded_row_id, id) |> assign(:expanded_action_type, action_atom)}

        {:error, message} ->
          send(self(), {:flash, :error, message})
          {:noreply, socket}
      end
    end
  end

  def handle_event("cancel_edit", _params, socket) do
    socket =
      socket
      |> assign(:editing_candidate_id, nil)
      |> assign(:edit_form, %{account_name: "", shares: 0, depot: ""})

    {:noreply, socket}
  end

  @impl true
  def handle_event("close_form", _params, socket) do
    socket =
      socket
      |> assign(:expanded_row_id, nil)
      |> assign(:expanded_action_type, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_children", %{"id" => id}, socket) do
    id = String.to_integer(id)
    expanded_parents = socket.assigns.expanded_parents

    # Toggle the expanded state for this parent
    expanded_parents =
      if Map.has_key?(expanded_parents, id) do
        # If it's already in the map, toggle its value
        if Map.get(expanded_parents, id) do
          # If it's currently expanded, remove it from the map (collapse it)
          Map.delete(expanded_parents, id)
        else
          # If it's explicitly set to not expanded, set it to expanded
          Map.put(expanded_parents, id, true)
        end
      else
        # If it's not in the map (default collapsed), add it as expanded
        Map.put(expanded_parents, id, true)
      end

    # The static indices are already calculated and won't change when expanding/collapsing
    # so we don't need to recalculate them here
    {:noreply, assign(socket, :expanded_parents, expanded_parents)}
  end

  def handle_event("update_status_filter", %{"status" => status}, socket) do
    send(self(), {:update_status_filter, status})
    send(self(), {:refresh_candidates})
    {:noreply, socket}
  end

  def handle_event("update_type_filter", %{"type" => type}, socket) do
    send(self(), {:update_type_filter, type})
    send(self(), {:refresh_candidates})
    {:noreply, socket}
  end

  def handle_event("update_sent_filter", %{"sent" => sent}, socket) do
    send(self(), {:update_sent_filter, sent})
    send(self(), {:refresh_candidates})
    {:noreply, socket}
  end

  @impl true
  def handle_event("unlink_contact", %{"id" => id}, socket) do
    id = String.to_integer(id)
    candidate = Gaia.BeneficialOwners.get_report_candidate!(id)

    case Gaia.BeneficialOwners.update_report_candidate(candidate, %{nominee_contact_id: nil}) do
      {:ok, _updated_candidate} ->
        send(self(), {:refresh_candidates})
        send(self(), {:flash, :info, "Contact unlinked successfully."})
        {:noreply, socket}

      {:error, _changeset} ->
        send(self(), {:flash, :error, "Failed to unlink contact."})
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("update_status", %{"id" => id, "status" => status}, socket) do
    id = String.to_integer(id)
    status = String.to_existing_atom(status)

    # Call the new context function to update the status of the candidate and all its children
    case Gaia.BeneficialOwners.update_report_candidate_status(id, status) do
      {:ok, %{candidate: _updated_candidate, update_children: %{count: _count}}} ->
        send(self(), {:refresh_candidates})
        {:noreply, socket}

      {:error, _step, _changeset, _changes} ->
        send(self(), {:flash, :error, "Failed to update status"})
        {:noreply, socket}
    end
  end

  # Helper function to calculate percentage with two decimal places
  defp calculate_percentage(child_shares, parent_shares)
       when is_number(child_shares) and is_number(parent_shares) and parent_shares > 0 do
    percentage = child_shares / parent_shares * 100
    # Format to two decimal places
    Float.round(percentage, 2)
  end

  defp calculate_percentage(_, _), do: 0.0

  # Helper function to determine the color class based on percentage
  defp percentage_color_class(percentage) when percentage < 90 do
    # or "text-yellow-500" for a warning color
    "text-red-500"
  end

  defp percentage_color_class(_), do: "text-green-500"

  # Helper function to check if a candidate has any address information
  defp has_address?(candidate) do
    not is_nil(candidate.address_line_one) ||
      not is_nil(candidate.address_line_two) ||
      not is_nil(candidate.address_city) ||
      not is_nil(candidate.address_state) ||
      not is_nil(candidate.address_postcode) ||
      not is_nil(candidate.address_country)
  end

  # Helper function to check if a candidate has second line address information
  defp has_address_line_2?(candidate) do
    not is_nil(candidate.address_city) ||
      not is_nil(candidate.address_state) ||
      not is_nil(candidate.address_country)
  end

  # Helper function to format the first line of the address
  defp format_address_line_1(candidate) do
    [candidate.address_line_one, candidate.address_line_two]
    |> Enum.filter(&(not is_nil(&1) and &1 != ""))
    |> Enum.join(", ")
  end

  # Helper function to format the second line of the address
  defp format_address_line_2(candidate) do
    [candidate.address_city, candidate.address_state, candidate.address_country]
    |> Enum.filter(&(not is_nil(&1) and &1 != ""))
    |> Enum.join(", ")
  end

  defp type_icon(type) do
    case type do
      :nominee -> "hero-building-office-2"
      :asic -> "hero-building-office"
      :owner -> "hero-user"
      _ -> "hero-question-mark-circle"
    end
  end

  defp type_color(type) do
    case type do
      :nominee -> "text-indigo-500 dark:text-indigo-400"
      :asic -> "text-amber-500 dark:text-amber-400"
      :owner -> "text-emerald-500 dark:text-emerald-400"
      _ -> "text-gray-500 dark:text-gray-400"
    end
  end

  defp name_cell_class(layer) do
    case layer do
      1 -> "flex items-start"
      2 -> "flex items-start pl-6"
      3 -> "flex items-start pl-12"
      _ -> "flex items-start"
    end
  end

  defp maybe_check_authorisation(:email, []) do
    {:error, "No authorisation letters found"}
  end

  defp maybe_check_authorisation(_, _), do: {:ok, nil}

  defp format_number_with_commas(number) when is_integer(number) do
    number
    |> Integer.to_string()
    |> String.reverse()
    |> String.replace(~r/.{3}(?=.)/, "\\0,")
    |> String.reverse()
  end

  defp status_icon(status) do
    case status do
      :done -> "hero-check-circle"
      :failed -> "hero-x-circle"
      :pending -> "hero-clock"
      :email -> "hero-envelope"
      _ -> "hero-question-mark-circle"
    end
  end

  defp status_color(status) do
    case status do
      :done -> "text-blue-500 dark:text-blue-400"
      :failed -> "text-red-500 dark:text-red-400"
      :pending -> "text-amber-500 dark:text-amber-400"
      :email -> "text-sky-500 dark:text-sky-400"
      _ -> "text-gray-500 dark:text-gray-400"
    end
  end

  # Private helper function to handle saving edits
  defp save_candidate_edit(id, socket) do
    edit_form = socket.assigns.edit_form
    candidate = Gaia.BeneficialOwners.get_report_candidate!(id)

    case Gaia.BeneficialOwners.update_report_candidate(candidate, %{
           account_name: edit_form.account_name,
           shares: edit_form.shares,
           depot: edit_form.depot
         }) do
      {:ok, updated_candidate} ->
        # Only update the existing asic members when the original shares is 0
        # Otherwise will overwrite the existing change
        if candidate.asic_organisation_id && candidate.shares == 0 do
          Gaia.BeneficialOwners.AsicImporter.create_candidates_from_existing_asic_members(
            candidate.asic_organisation_id,
            updated_candidate
          )
        end

        send(self(), {:refresh_candidates})

        socket =
          socket
          |> assign(:editing_candidate_id, nil)
          |> assign(:edit_form, %{account_name: "", shares: 0, depot: ""})

        {:noreply, socket}

      {:error, _changeset} ->
        send(self(), {:flash, :error, "Failed to update"})
        {:noreply, socket}
    end
  end

  # Helper function to check if a candidate is expanded
  defp is_expanded?(candidate_id, expanded_parents) do
    # Check if this specific candidate is expanded
    Map.get(expanded_parents, candidate_id, false)
  end

  # Helper function to generate hierarchy line indicators
  defp hierarchy_line_indicator(layer, is_last_child) do
    case layer do
      1 ->
        ""

      2 ->
        if is_last_child do
          # For the last child, the line should only extend to the middle of the row
          ~s(<div class="absolute left-0 top-0 bottom-[50%] border-l border-indigo-400 dark:border-indigo-600 ml-2"></div>)
        else
          # For non-last children, the line should extend to the bottom to connect with siblings
          ~s(<div class="absolute left-0 top-0 bottom-0 border-l border-indigo-400 dark:border-indigo-600 ml-2"></div>)
        end

      3 ->
        parent_line =
          ~s(<div class="absolute left-0 top-0 bottom-0 border-l border-indigo-400 dark:border-indigo-600 ml-2"></div>)

        child_line =
          if is_last_child do
            # For the last grandchild, the line should only extend to the middle of the row
            ~s(<div class="absolute left-0 top-0 bottom-[50%] border-l border-indigo-400 dark:border-indigo-600 ml-8"></div>)
          else
            # For non-last grandchildren, the line should extend to the bottom to connect with siblings
            ~s(<div class="absolute left-0 top-0 bottom-0 border-l border-indigo-400 dark:border-indigo-600 ml-8"></div>)
          end

        parent_line <> child_line

      _ ->
        ""
    end
  end
end
