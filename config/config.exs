import Config

################################################################################
# arc                                                                          #
################################################################################

config :arc,
  storage: Arc.Storage.GCS,
  bucket: System.get_env("GOOGLE_STORAGE_BUCKET"),
  # File expires in 1 hour by default
  expires_in: 60 * 60

################################################################################
# athena                                                                       #
################################################################################

config :athena, AthenaWeb.Endpoint,
  url: [host: "localhost.com"],
  render_errors: [view: AthenaWeb.ErrorView, accepts: ~w(json), layout: false],
  pubsub_server: Gaia.PubSub,
  live_view: [signing_salt: "rPRsXl7i"]

################################################################################
# esbuild                                                                      #
################################################################################

config :esbuild,
  version: "0.14.0",
  default: [
    args: ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../apps/hades/assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

################################################################################
# fun_with_flags                                                               #
################################################################################

config :fun_with_flags, :cache,
  enabled: true,
  ttl: 900

config :fun_with_flags, :persistence,
  adapter: FunWithFlags.Store.Persistent.Ecto,
  repo: Gaia.Repo,
  ecto_table_name: "configurations_feature_flags"

config :fun_with_flags, :cache_bust_notifications,
  enabled: true,
  adapter: FunWithFlags.Notifications.PhoenixPubSub,
  client: Gaia.PubSub

################################################################################
# gaia                                                                         #
################################################################################

config :gaia, ecto_repos: [Gaia.Repo]

config :gaia, Oban,
  repo: Gaia.Repo,
  plugins: [
    # Set Oban to retain historic jobs for 3 days
    {Oban.Plugins.Pruner, max_age: 60 * 60 * 24 * 3},
    {Oban.Plugins.Cron,
     crontab: [
       # Import Announcements Mon - Fri
       # ASX - Every 1 minute between 8am and 4pm business days
       {"*/1 8-16 * * 1-5", Gaia.Worker.ImportAnnouncements},
       {"*/10 17-23,0-7 * * 1-5", Gaia.Worker.ImportAnnouncements},
       {"*/10 8-16 * * 1-5", Gaia.Worker.ImportAnnouncementsCheck},
       {"*/10 * * * *", Gaia.Worker.HubUptimeChecker},
       # AQSE/LSE RNS - Every 1 minute between 7am and 7pm (London local time) business days
       # Shortest timezone difference with Sydney is 9 hours
       # Longest timezone difference with Sydney is 11 hours
       {"*/1 16-23 * * 1-5", Gaia.Workers.PreProcessImportRNS},
       {"*/1 0-6 * * 2-6", Gaia.Workers.PreProcessImportRNS},
       {"*/10 7-15 * * 1-6", Gaia.Workers.PreProcessImportRNS},
       {"*/1 16-23 * * 1-5", Gaia.Workers.ImportRNS},
       {"*/1 0-6 * * 2-6", Gaia.Workers.ImportRNS},
       {"*/10 7-15 * * 1-6", Gaia.Workers.ImportRNS},
       # Import automic, boardroom, computershare, link and xcend registry data at 2am everyday
       {"0 2 * * *", Gaia.Workers.ImportRegister, args: %{market_keys: [:asx]}},
       # Import neville registry data at 9am everyday which is between 10pm - midnight in UK
       {"0 9 * * *", Gaia.Workers.ImportRegister, args: %{market_keys: [:aqse, :lse]}},
       # Every 1st day of month, pick a random company from Automic and Boardroom to check for data disrepancy between registry and our database
       {"0 8 1 * *", Gaia.Workers.RandomCheckAutomicClient},
       {"0 8 1 * *", Gaia.Workers.RandomCheckBoardroomClient},
       # Notify Top 50 shareholders movements for ASX companies at 8.30am everyday
       {"30 8 * * *", Gaia.Workers.NotifyTopFiftyShareholderMovements, args: %{market_key: :asx}},
       # Notify Top 50 shareholders movements for AQSE companies between 8 - 10am UK everyday
       {"0 19 * * *", Gaia.Workers.NotifyTopFiftyShareholderMovements, args: %{market_key: :aqse}},
       # Notify Top 50 shareholders movements for LSE companies between 8 - 10am UK everyday
       {"0 19 * * *", Gaia.Workers.NotifyTopFiftyShareholderMovements, args: %{market_key: :lse}},
       # Update shareholder age/income estimates at 8am on Saturday
       # NOTE We run this once a week to reduce number of retries, not optimal - needs rework!
       {"0 8 * * 6", Gaia.Workers.UpdateShareholderDemographicsInfoForAllCompanies, args: %{market_key: :asx}},
       # Send custom domain nudge emails at 9.30am everyday
       # Disabling for now as some companies have hub domain set up but not email DNS records, so they get this every morning
       # {"30 9 * * *", Gaia.Workers.SendCustomDomainNudgeEmails},
       # Send shareholding info follow up emails at 9.15am (Australia) everyday
       {"15 9 * * *", Gaia.Workers.ShareholdingInfoFollowUpEmails, args: %{market_key: :asx}},
       # Send shareholding info follow up emails at 9.15am (UK - LSE) everyday
       {"15 20 * * *", Gaia.Workers.ShareholdingInfoFollowUpEmails, args: %{market_key: :lse}},
       # Send shareholding info follow up emails at 9.15am (UK - AQSE) everyday
       {"15 20 * * *", Gaia.Workers.ShareholdingInfoFollowUpEmails, args: %{market_key: :aqse}},
       # Send qualified investor info follow up emails at 9.45am (Australia) everyday
       {"45 9 * * *", Gaia.Workers.QualifiedInvestorInfoFollowUpEmails, args: %{market_key: :asx}},
       # Send qualified investor info follow up emails at 9.45am (UK - LSE) everyday
       {"45 20 * * *", Gaia.Workers.QualifiedInvestorInfoFollowUpEmails, args: %{market_key: :lse}},
       # Send qualified investor info follow up emails at 9.45am (UK - AQSE) everyday
       {"45 20 * * *", Gaia.Workers.QualifiedInvestorInfoFollowUpEmails, args: %{market_key: :aqse}},
       # Retrieving fresh refinitiv API token hourly
       {"0 * * * *", Gaia.Worker.RefinitivToken},
       # Import non adjusted timeseries at midnight Tuesday to Saturday (AU time)
       {"0 0 * * 2-6", Gaia.Workers.ImportTimeseriesNonAdjusted, args: %{market_keys: [:asx]}},
       # Import non adjusted timeseries at midnight Tuesday to Saturday (UK time)
       {"0 7 * * 2-6", Gaia.Workers.ImportTimeseriesNonAdjusted, args: %{market_keys: [:aqse, :lse]}},
       # Retrieving latest market cap for each organisation at 9am Monday - Friday
       {"0 9 * * 1-5", Gaia.Workers.RetrieveOrganisationLatestMarketData},
       # Import Ticker at 1am everyday
       {"0 1 * * *", Gaia.Workers.ImportListingKey},
       # Runs on every tuesday
       {"0 6 * * 2", Gaia.Workers.UpdateDemoCompanies, args: %{purpose: :registry_data}},
       # Runs everyday at 6am
       {"0 6 * * *", Gaia.Workers.UpdateDemoCompanies, args: %{purpose: :utm_analytics}},
       # Notify placement post raise analysis ready daily at 10.30am
       {"30 10 * * *", Gaia.Workers.NotifyPlacementPostRaiseAnalysisReady, args: %{market_key: :asx}},
       # Send Slack notification to scraping most discussed stock from hot copper
       {"30 16 * * *", Gaia.Workers.MostDiscussedStockFromHotcopper},
       # Refreshes LinkedIn access tokens if they are about to expire within 8 days every Monday morning
       {"0 8 * * 1", Gaia.Workers.CheckExpiringLinkedInAccessTokens},
       # Sends an email reminder to companies whose LinkedIn refresh tokens are about to expire at 9:00am everyday (Australia)
       {"0 9 * * *", Gaia.Workers.CheckExpiringLinkedInRefreshTokens, args: %{market_key: :asx}},
       # Sends an email reminder to companies whose LinkedIn refresh tokens are about to expire at 9:00am everyday (UK - LSE)
       {"0 20 * * *", Gaia.Workers.CheckExpiringLinkedInRefreshTokens, args: %{market_key: :lse}},
       # Sends an email reminder to companies whose LinkedIn refresh tokens are about to expire at 9:00am everyday (UK - AQSE)
       {"0 20 * * *", Gaia.Workers.CheckExpiringLinkedInRefreshTokens, args: %{market_key: :aqse}},
       # Monitor marketing email reputation of companies everyday at 9am
       {"0 9 * * *", Gaia.Workers.MonitorCompaniesEmailReputations},
       # Midnight daily - find any contacts that haven't had their emails verified and verify them, suppressing the invalid ones
       {"0 0 * * *", Gaia.Workers.VerifyAllContactEmails},
       # Checking AWS SES status for each organisation with verified email domain at 10am Monday - Friday
       {"0 10 * * 1-5", Gaia.Workers.CheckAwsSesStatusForAllVerifiedEmailDomains},
       # Check email reputation of companies every monday at 9am
       {"0 9 * * 1", Gaia.Workers.CheckEmailReputation},
       # Cache email reputation for companies every monday at 1am
       {"0 1 * * 1", Gaia.Workers.WeeklyTrackingEmailReputation},
       # Scrape ASX announcements for SPP historical table at 8am Saturday
       # NOTE This is not a crucial task, will not change much week to week
       {"0 8 * * 6", Gaia.Workers.ScrapeAsxAnnouncementForSpp},
       # Calculate the estimated_contacts_size for all dynamic lists at midnight everyday
       {"0 0 * * *", Gaia.Workers.CalculateDynamicListSizes},
       # Import data from Relait at 10pm everyday
       {"0 22 * * *", Gaia.Workers.ImportFromRelait},
       # Import data from Relait at 11pm everyday
       {"0 23 * * *", Gaia.Workers.UpdateHubspotCompanyProperties},
       # Update the SPP prediction at 9am Sunday each week
       # NOTE This is run on the Sunday so that the shareholder demographics job finishes, not optimal - needs rework to run after demographics!
       {"0 9 * * 0", Gaia.Workers.UpdateSppPredictionsForAllCompanies, args: %{market_key: :asx}},
       # Delete outdated (offsite 100) website data at 10pm every Sunday
       {"0 22 * * 0", Gaia.Workers.DeleteOutdatedWebsiteData},
       # Update LinkedIn statistics every 4 hours
       {"0 */4 * * *", Gaia.Workers.UpdateLinkedinStatistics},
       # Update media stats for recent media (last 2 weeks) every hour
       {"0 * * * *", Gaia.Workers.RefreshRecentMediaStats},
       # Update media stats for older media (beyond 2 weeks) daily at midnight
       {"0 0 * * *", Gaia.Workers.RefreshOlderMediaStats},
       # Weekly cleanup of very stale media stats (Sunday at 3am)
       {"0 3 * * 0", Gaia.Workers.RefreshStaleMediaStats, args: %{stale_days: 7, max_batches: 25}},
       # Once a week on Sunday at 5am
       {"0 5 * * 0", Gaia.Workers.Scheduler,
        queue: :schedulers, args: %{worker: Gaia.Workers.CalculateContactEmailScore}},
       # Once a week on Saturday at 5am
       {"0 5 * * 6", Gaia.Workers.Scheduler, queue: :schedulers, args: %{worker: Gaia.Workers.CalculateInvestorHubScore}}
     ],
     timezone: "Australia/Sydney"},
    Oban.Web.Plugins.Stats
  ],
  # Queue limits are local (per-node), not global (per-cluster)
  # Be careful how many concurrent jobs make expensive system calls (ie. use heavy_event for resource heavy job)
  queues: [
    default: 10,
    heavy_event: 1,
    campaign_email: 5,
    automic: 2,
    boardroom: 2,
    campaign_mail: 1,
    emails: 40,
    asx_announcements: 1,
    rns: 1,
    rns_high_priority: 3,
    rns_pre_process: 1,
    send_email: 1,
    verify_email_list: 1,
    contact_list: 1,
    data_science: 2,
    board_report: 1,
    uptime_checker: 1,
    xcend: 1,
    schedulers: 10,
    media_stats: 15
  ],
  notifier: Oban.Notifiers.PG

################################################################################
# mailer                                                                       #
################################################################################
config :email_transactional, EmailTransactionalWeb.Endpoint,
  url: [host: "localhost.com"],
  pubsub_server: Gaia.PubSub

################################################################################
# tracking                                                                     #
################################################################################
config :tracking, TrackingWeb.Endpoint,
  url: [host: "localhost.com"],
  render_errors: [view: TrackingWeb.ErrorView, accepts: ~w(json), layout: false],
  pubsub_server: Gaia.PubSub,
  live_view: [signing_salt: "eBh5PCca"]

################################################################################
# hades                                                                        #
################################################################################

config :hades, HadesWeb.Endpoint,
  url: [host: "localhost.com"],
  render_errors: [view: HadesWeb.ErrorView, accepts: ~w(html json), layout: false],
  pubsub_server: Gaia.PubSub,
  live_view: [signing_salt: "m2d3C3XD"]

################################################################################
# hermes                                                                       #
################################################################################

config :hermes, HermesWeb.Endpoint,
  url: [host: "localhost.com"],
  render_errors: [view: HermesWeb.ErrorView, accepts: ~w(json), layout: false],
  pubsub_server: Gaia.PubSub,
  live_view: [signing_salt: "XJMy5GiG"]

################################################################################
# logger                                                                       #
################################################################################

config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]

################################################################################
# phoenix                                                                      #
################################################################################

config :phoenix, :json_library, Jason
config :absinthe_graphql_ws, :json_library, Jason

################################################################################
# public_api                                                                   #
################################################################################

config :public_api, PublicApiWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4006],
  live_view: [signing_salt: "288ENyYD"],
  pubsub_server: Gaia.PubSub,
  render_errors: [
    formats: [json: PublicApiWeb.ErrorJSON],
    layout: false
  ]

################################################################################
# heimdallr                                                                    #
################################################################################

config :heimdallr, HeimdallrWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4007],
  live_view: [signing_salt: "2fdSIcaD"],
  pubsub_server: Gaia.PubSub,
  render_errors: [
    formats: [json: HeimdallrWeb.ErrorJSON],
    layout: false
  ]

################################################################################
# segment                                                                      #
################################################################################

config :segment,
  retry_attempts: 3,
  retry_expiry: 10_000,
  retry_start: 100,
  send_to_http: true

################################################################################
# ueberauth                                                                    #
################################################################################

config :ueberauth, Ueberauth,
  providers: [
    facebook: {Ueberauth.Strategy.Facebook, []},
    google: {Ueberauth.Strategy.Google, [default_scope: "email profile"]},
    google_athena: {Ueberauth.Strategy.Google, [default_scope: "email profile", callback_path: "/auth/google/callback"]},
    google_heimdallr:
      {Ueberauth.Strategy.Google, [default_scope: "email profile", callback_path: "/auth/google/callback"]},
    microsoft: {Ueberauth.Strategy.Microsoft, [callback_path: "/auth/microsoft/callback"]},
    linkedin: {Ueberauth.Strategy.LinkedIn, [default_scope: "r_emailaddress r_liteprofile"]},
    twitter: {Ueberauth.Strategy.Twitter, []}
  ]

################################################################################
# Link Share Registry                                                          #
################################################################################
config :link,
  mft_user: System.get_env("LINK_MFT_USER") || "",
  ssh_dir: System.get_env("LINK_SSH_DIR", "~/.ssh"),
  ssh_public_key_name: System.get_env("LINK_SSH_PUBLIC_KEY_NAME") || "link_public_key.pem",
  ssh_private_key_name: System.get_env("LINK_SSH_PRIVATE_KEY_NAME") || "link_private_key.pem",
  ssh_private_key_passphrase: System.get_env("LINK_SSH_PRIVATE_KEY_PASSPHRASE") || "",
  gpg_dir: System.get_env("LINK_GPG_DIR", "~/.gnupg"),
  gpg_public_key_name: System.get_env("LINK_GPG_PUBLIC_KEY_NAME") || "link_gpg_public_key.asc",
  gpg_private_key_name: System.get_env("LINK_GPG_PRIVATE_KEY_NAME") || "link_gpg_private_key.asc",
  gpg_private_key_passphrase: System.get_env("LINK_GPG_PRIVATE_KEY_PASSPHRASE") || ""

################################################################################
# Email Checker                                                                #
################################################################################
config :email_checker,
  default_dns: :system,
  also_dns: [],
  validations: [EmailChecker.Check.Format, EmailChecker.Check.MX],
  smtp_retries: 2,
  timeout_milliseconds: :infinity

################################################################################
# Gaia                                                                         #
################################################################################

config :gaia, FileService,
  copy_adapter: &GoogleAPI.Storage.move_objects_same_location/3,
  delete_adapter: &GoogleAPI.Storage.delete_object/1,
  get_adapter: &GoogleAPI.Storage.get_object/1,
  file_actions: [
    {Gaia.FileManagement.Actions.FileDnsAction, disabled: true},
    {Gaia.FileManagement.Actions.BoardReportAction, disabled: true},
    {Gaia.FileManagement.Actions.ShareholderPageAction, disabled: true},
    {Gaia.FileManagement.Actions.WelcomePageAction, disabled: true},
    {Gaia.FileManagement.Actions.ProfileLogoAction, disabled: true},
    Gaia.FileManagement.Actions.InvestorCertificateAction
  ]

config :googleapi, :storage, adapter: GoogleAPI.Storage.GcsAdapter

config :gaia, CandidateImporterAi, ai_parsing: &Gaia.BeneficialOwners.CandidateImporterAiParser.ai_parsing/4

config :elixir, :time_zone_database, Tzdata.TimeZoneDatabase

import_config "#{config_env()}.exs"
