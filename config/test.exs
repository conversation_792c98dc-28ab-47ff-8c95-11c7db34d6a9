import Config
################################################################################
# arc                                                                          #
################################################################################

config :arc, storage: Arc.Storage.Local

################################################################################
# athena                                                                       #
################################################################################

config :athena, AthenaWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4040],
  secret_key_base: "id+5VPW/+LLjr5X8DOiB7URijOMWOGnVwx6zTHzHXRXIeibjjJ9JcUyz/PGtMfsc",
  server: false

################################################################################
# email                                                                        #
################################################################################

config :email_transactional, EmailTransactional.Mailer, adapter: Swoosh.Adapters.Local

config :email_marketing, EmailMarketing.Mailer, adapter: Swoosh.Adapters.Local

################################################################################
# gaia                                                                         #
################################################################################

config :gaia, Gaia.Repo,
  pool: Ecto.Adapters.SQL.Sandbox,
  database: System.get_env("TEST_DB_NAME") || "leaf_test",
  username: System.get_env("TEST_DB_USER") || "postgres",
  password: System.get_env("TEST_DB_PASS") || "postgres",
  hostname: System.get_env("TEST_DB_HOST") || "localhost",
  # use port 5434 for Docker Compose test config
  port: System.get_env("TEST_DB_PORT") || 5432,
  migration_lock: :pg_advisory_lock

# Disabled the oban job for testing enviroment
config :gaia, Oban, queues: false, plugins: false, testing: :manual

################################################################################
# tracking                                                                     #
################################################################################
config :tracking, TrackingWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4041],
  secret_key_base: "nX5KosBRSw6Jgg+PAMuNrM2BAAX3zbZPbhZAfVR1Kez5aV/QblcAxNYkuaXJ8qqL",
  server: false

################################################################################
# hades                                                                        #
################################################################################

config :hades, HadesWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4040],
  secret_key_base: "nX5KosBRSw6Jgg+PAMuNrM2BAAX3zbZPbhZAfVR1Kez5aV/QblcAxNYkuaXJ8qqL",
  server: false

################################################################################
# helper                                                                       #
################################################################################

config :helper,
  athena_url: "http://localhost.com:3001",
  athena_web_url: "http://localhost.com:4001",
  environment: System.get_env("LEAF_ENVIRONMENT", "development"),
  hades_url: "http://localhost.com:4002",
  hermes_url: "http://localhost.com:4003",
  hash_salt: "ciaPv",
  runtime_env: "test",
  error_handler_impl: Helper.Error.Custom.ErrorHandlerMock

################################################################################
# hermes                                                                       #
################################################################################

config :hermes, HermesWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4040],
  secret_key_base: "Cb2HQ+tzH7nbRmmSMEqXTN5ZwxM8Hvc9W+168xfv3CBnZhXJ981AUKdJLIrvxdQ2",
  server: false

################################################################################
# heimdallr                                                                    #
################################################################################
config :heimdallr, HeimdallrWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4040],
  secret_key_base: "Cb2HQ+tzH7nbRmmSMEqXTN5ZwxM8Hvc9W+168xfv3CBnZhXJ981AUKdJLIrvxdQ2",
  server: false

################################################################################
# logger                                                                       #
################################################################################

config :logger, level: :warning
# Uncomment the line below to see database calls in logs
# config :logger, level: :debug

################################################################################
# public_api                                                                   #
################################################################################
config :public_api, PublicApiWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4040],
  secret_key_base: "/bQXBj0nf5osuy1EXD0N34+tLqllkCEIA8wKjwv4EJfFxyvrLKw2scmaXNuo8fVa",
  server: false

################################################################################
# vault                                                                        #
################################################################################

config :gaia, vault_key: 32 |> :crypto.strong_rand_bytes() |> Base.encode64()

# This config is needed for tests to run without warnings. Futher configured in runtime.exs for production/staging environments
config :sentry,
  environment_name: Mix.env(),
  enable_source_code_context: true,
  root_source_code_paths: [File.cwd!()]

################################################################################
# Gaia Company File Service                                                    #
################################################################################

config :googleapi, :storage, adapter: StorageAdapterMock

config :gaia, FileService,
  copy_adapter: &GoogleAPI.Storage.move_objects_same_location/3,
  delete_adapter: &GoogleAPI.Storage.delete_object/1,
  get_adapter: &GoogleAPI.Storage.get_object/1,
  file_actions: [
    FileManagementActionMock,
    {FileManagementActionMock, disabled: true}
  ]

################################################################################
# Athena                                                                       #
################################################################################

config :athena,
  log_email_basic_auth: %{username: "secure_username", password: "secure_password"}

################################################################################
# QuoteMedia                                                                   #
################################################################################

config :quote_media,
  webmaster_id: "webmaster_id"

################################################################################
# Cloudex (for Cloudinary)                                                     #
################################################################################
config :cloudex,
  api_key: System.get_env("CLOUDINARY_API_KEY"),
  secret: System.get_env("CLOUDINARY_API_SECRET"),
  cloud_name: System.get_env("CLOUDINARY_CLOUD_NAME"),
  root_folder: System.get_env("CLOUDINARY_ROOT_FOLDER_NAME")

################################################################################
# AI Parsing                                                                   #
################################################################################

config :gaia, CandidateImporterAi, ai_parsing: &Gaia.BeneficialOwners.CandidateImporterAiMock.ai_parsing/4
